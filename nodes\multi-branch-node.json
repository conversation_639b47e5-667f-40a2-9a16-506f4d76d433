{"id": "multi-branch-node", "name": "多分支节点", "type": "multi-branch", "version": "1.0.0", "description": "多路径分发数据", "metadata": {"category": "控制流", "tags": ["routing", "branching", "switch", "multiple-output"], "author": "Dynamic Workflow System", "documentation": "Evaluates multiple conditions and routes data to corresponding output branches"}, "pins": {"input": {"count": 1, "dynamic": false, "definitions": [{"name": "data", "label": "Input Data", "dataType": "any", "required": true, "description": "Data to route to appropriate branch"}]}, "output": {"count": 3, "dynamic": true, "min": 2, "max": 10, "definitions": [{"name": "branch1", "label": "Branch 1", "dataType": "any", "description": "First output branch"}, {"name": "branch2", "label": "Branch 2", "dataType": "any", "description": "Second output branch"}, {"name": "default", "label": "<PERSON><PERSON><PERSON>", "dataType": "any", "description": "Default output when no conditions match"}]}}, "visual": {"icon": {"type": "unicode", "value": "🌿", "color": "#FF9800"}, "shape": {"type": "hexagon"}, "sizing": {"baseWidth": 100, "baseHeight": 60, "pinSpacing": 20, "dynamicResize": true}, "colors": {"background": "#FFF3E0", "border": "#FF9800", "text": "#E65100"}}, "backend": {"endpoint": "/api/nodes/multi-branch", "method": "POST", "code": "async function execute(req, res) {\n  try {\n    const { data } = req.body.inputs;\n    const { branches } = req.body.config;\n    \n    let matchedBranch = null;\n    \n    // Evaluate each branch condition\n    for (const branch of branches) {\n      if (branch.condition) {\n        try {\n          const result = new Function('data', `return ${branch.condition}`)(data);\n          if (result) {\n            matchedBranch = branch.name;\n            break;\n          }\n        } catch (error) {\n          console.error(`Error evaluating condition for ${branch.name}:`, error);\n        }\n      }\n    }\n    \n    // Use default branch if no condition matched\n    if (!matchedBranch) {\n      matchedBranch = 'default';\n    }\n    \n    const outputs = {};\n    outputs[matchedBranch] = data;\n    \n    res.json({ success: true, outputs });\n  } catch (error) {\n    res.status(400).json({ success: false, error: error.message });\n  }\n}", "dependencies": [], "middleware": ["express.json()"]}, "frontend": {"executable": {"code": "class MultiBranchNode {\n  constructor(nodeId, config) {\n    this.nodeId = nodeId;\n    this.config = config;\n    this.inputs = {};\n    this.outputs = {};\n    this.branches = config.branches || [\n      { name: 'branch1', condition: 'data.type === \"A\"' },\n      { name: 'branch2', condition: 'data.type === \"B\"' },\n      { name: 'default', condition: null }\n    ];\n  }\n  \n  async execute() {\n    if (!this.inputs.data) {\n      return; // Wait for input data\n    }\n    \n    try {\n      const data = this.inputs.data;\n      let matchedBranch = null;\n      \n      // Evaluate each branch condition\n      for (const branch of this.branches) {\n        if (branch.condition) {\n          try {\n            const result = new Function('data', `return ${branch.condition}`)(data);\n            if (result) {\n              matchedBranch = branch.name;\n              break;\n            }\n          } catch (error) {\n            console.error(`Error evaluating condition for ${branch.name}:`, error);\n          }\n        }\n      }\n      \n      // Use default branch if no condition matched\n      if (!matchedBranch) {\n        matchedBranch = 'default';\n      }\n      \n      this.sendOutput(matchedBranch, data);\n      \n      // Clear inputs for next execution\n      this.inputs = {};\n    } catch (error) {\n      console.error('Multi-branch node error:', error);\n    }\n  }\n  \n  receiveInput(pinName, data) {\n    this.inputs[pinName] = data;\n    this.execute();\n  }\n  \n  sendOutput(pinName, data) {\n    if (this.outputs[pinName]) {\n      this.outputs[pinName].forEach(connection => {\n        connection.targetNode.receiveInput(connection.targetPin, data);\n      });\n    }\n  }\n}", "dependencies": []}, "properties": {"panel": [{"type": "number", "name": "branchCount", "label": "Number of Branches", "defaultValue": 3, "validation": {"required": true, "min": 2, "max": 10}}, {"type": "textarea", "name": "branchConditions", "label": "Branch Conditions (JSON)", "defaultValue": "[\n  {\"name\": \"branch1\", \"condition\": \"data.type === 'A'\"},\n  {\"name\": \"branch2\", \"condition\": \"data.type === 'B'\"},\n  {\"name\": \"default\", \"condition\": null}\n]"}]}}}