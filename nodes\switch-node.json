{"id": "switch-node", "name": "Switch Node", "type": "control", "version": "1.0.0", "description": "Routes input to one of multiple outputs based on condition", "metadata": {"category": "Control Flow", "tags": ["switch", "routing", "conditional", "branch"], "author": "Dynamic Workflow System", "documentation": "Evaluates input and routes to appropriate output branch"}, "pins": {"input": {"count": 2, "dynamic": false, "definitions": [{"name": "value", "label": "Input Value", "dataType": "any", "required": true, "description": "Value to evaluate for routing"}, {"name": "condition", "label": "Switch Condition", "dataType": "string", "required": false, "description": "Condition expression for routing logic"}]}, "output": {"count": 3, "dynamic": true, "min": 2, "max": 10, "definitions": [{"name": "case1", "label": "Case 1", "dataType": "any", "description": "First output case"}, {"name": "case2", "label": "Case 2", "dataType": "any", "description": "Second output case"}, {"name": "default", "label": "<PERSON><PERSON><PERSON>", "dataType": "any", "description": "Default output case"}]}}, "visual": {"icon": {"type": "unicode", "value": "🔀", "color": "#9C27B0"}, "shape": {"type": "rectangle"}, "sizing": {"baseWidth": 140, "baseHeight": 100, "pinSpacing": 25, "dynamicResize": true}, "colors": {"background": "#F3E5F5", "border": "#9C27B0", "text": "#4A148C"}}, "backend": {"endpoint": "/api/nodes/switch", "method": "POST", "code": "async function execute(req, res) {\n  try {\n    const { inputs, config } = req.body;\n    \n    const value = inputs.value;\n    const condition = inputs.condition || config.switchCondition || 'value';\n    const cases = config.cases || ['case1', 'case2', 'default'];\n    \n    // Simple switch logic\n    let selectedCase = 'default';\n    \n    if (typeof value === 'number') {\n      const caseIndex = Math.floor(value) % (cases.length - 1);\n      selectedCase = cases[caseIndex] || 'default';\n    } else if (typeof value === 'string') {\n      const hash = value.length % (cases.length - 1);\n      selectedCase = cases[hash] || 'default';\n    }\n    \n    const outputs = {};\n    outputs[selectedCase] = value;\n    \n    res.json({\n      success: true,\n      outputs: outputs,\n      metadata: {\n        selectedCase: selectedCase,\n        availableCases: cases\n      }\n    });\n  } catch (error) {\n    console.error('Switch node error:', error);\n    res.status(500).json({\n      success: false,\n      error: error.message\n    });\n  }\n}", "dependencies": []}, "frontend": {"executable": {"code": "class SwitchNode {\n  constructor(nodeData, eventEmitter) {\n    this.nodeData = nodeData;\n    this.eventEmitter = eventEmitter;\n    this.config = nodeData.config || {};\n  }\n  \n  async execute(inputs) {\n    try {\n      const value = inputs.value;\n      const condition = inputs.condition || this.config.switchCondition || 'value';\n      const cases = this.config.cases || ['case1', 'case2', 'default'];\n      \n      // Simple switch logic\n      let selectedCase = 'default';\n      \n      if (typeof value === 'number') {\n        const caseIndex = Math.floor(value) % (cases.length - 1);\n        selectedCase = cases[caseIndex] || 'default';\n      } else if (typeof value === 'string') {\n        const hash = value.length % (cases.length - 1);\n        selectedCase = cases[hash] || 'default';\n      }\n      \n      const outputs = {};\n      outputs[selectedCase] = value;\n      \n      return {\n        outputs: outputs\n      };\n    } catch (error) {\n      throw new Error(`Switch node failed: ${error.message}`);\n    }\n  }\n  \n  updateConfig(newConfig) {\n    this.config = { ...this.config, ...newConfig };\n  }\n}", "dependencies": []}, "properties": {"panel": [{"type": "select", "name": "switchMode", "label": "Switch Mode", "defaultValue": "auto", "options": [{"value": "auto", "label": "Auto (based on value type)"}, {"value": "numeric", "label": "Numeric Index"}, {"value": "string", "label": "String Hash"}, {"value": "custom", "label": "Custom Expression"}], "description": "How to determine which output to use"}, {"type": "textarea", "name": "switchCondition", "label": "Custom Condition", "defaultValue": "value % 2 === 0 ? 'case1' : 'case2'", "validation": {"required": false}, "description": "JavaScript expression for custom routing (use 'value' variable)"}, {"type": "number", "name": "outputCount", "label": "Number of Output Cases", "defaultValue": 3, "validation": {"min": 2, "max": 10}, "description": "Number of output cases (excluding default)"}]}}}