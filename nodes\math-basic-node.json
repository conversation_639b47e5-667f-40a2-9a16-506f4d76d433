{"id": "math-basic-node", "name": "Basic Math Operations", "type": "transform", "version": "1.0.0", "description": "Performs basic mathematical operations on two numbers", "metadata": {"category": "Math", "tags": ["math", "arithmetic", "calculation", "numbers"], "author": "Dynamic Workflow System", "documentation": "Calculates addition, subtraction, multiplication, and division of two input numbers"}, "pins": {"input": {"count": 2, "dynamic": false, "definitions": [{"name": "a", "label": "Number A", "dataType": "number", "required": true, "description": "First number for calculation"}, {"name": "b", "label": "Number B", "dataType": "number", "required": true, "description": "Second number for calculation"}]}, "output": {"count": 4, "dynamic": false, "definitions": [{"name": "add", "label": "Addition (A + B)", "dataType": "number", "description": "Sum of A and B"}, {"name": "subtract", "label": "Subtraction (A - B)", "dataType": "number", "description": "Difference of A and B"}, {"name": "multiply", "label": "Multiplication (A × B)", "dataType": "number", "description": "Product of A and B"}, {"name": "divide", "label": "Division (A ÷ B)", "dataType": "number", "description": "Quotient of A and B"}]}}, "visual": {"icon": {"type": "unicode", "value": "➕", "color": "#FF5722"}, "shape": {"type": "rectangle"}, "sizing": {"baseWidth": 150, "baseHeight": 100, "pinSpacing": 20}, "colors": {"background": "#FFF3E0", "border": "#FF5722", "text": "#BF360C"}}, "backend": {"endpoint": "/api/nodes/math-basic", "method": "POST", "code": "async function execute(req, res) {\n  try {\n    const { inputs, config } = req.body;\n    \n    // Parse input numbers\n    const a = parseFloat(inputs.a) || 0;\n    const b = parseFloat(inputs.b) || 0;\n    \n    // Perform calculations\n    const add = a + b;\n    const subtract = a - b;\n    const multiply = a * b;\n    \n    // Handle division by zero\n    let divide;\n    if (b === 0) {\n      if (config.divisionByZeroHandling === 'infinity') {\n        divide = a > 0 ? Infinity : a < 0 ? -Infinity : NaN;\n      } else {\n        divide = null;\n      }\n    } else {\n      divide = a / b;\n    }\n    \n    // Apply precision if configured\n    const precision = config.decimalPrecision || -1;\n    const formatNumber = (num) => {\n      if (num === null || !isFinite(num)) return num;\n      return precision >= 0 ? parseFloat(num.toFixed(precision)) : num;\n    };\n    \n    res.json({\n      success: true,\n      outputs: {\n        add: formatNumber(add),\n        subtract: formatNumber(subtract),\n        multiply: formatNumber(multiply),\n        divide: formatNumber(divide)\n      },\n      metadata: {\n        inputA: a,\n        inputB: b,\n        divisionByZero: b === 0\n      }\n    });\n  } catch (error) {\n    console.error('Math basic operation error:', error);\n    res.status(500).json({\n      success: false,\n      error: error.message\n    });\n  }\n}", "dependencies": []}, "frontend": {"executable": {"code": "class MathBasicNode {\n  constructor(nodeData, eventEmitter) {\n    this.nodeData = nodeData;\n    this.eventEmitter = eventEmitter;\n    this.config = nodeData.config || {};\n  }\n  \n  async execute(inputs) {\n    try {\n      // Parse input numbers\n      const a = parseFloat(inputs.a) || 0;\n      const b = parseFloat(inputs.b) || 0;\n      \n      // Perform calculations\n      const add = a + b;\n      const subtract = a - b;\n      const multiply = a * b;\n      \n      // Handle division by zero\n      let divide;\n      if (b === 0) {\n        if (this.config.divisionByZeroHandling === 'infinity') {\n          divide = a > 0 ? Infinity : a < 0 ? -Infinity : NaN;\n        } else {\n          divide = null;\n        }\n      } else {\n        divide = a / b;\n      }\n      \n      // Apply precision if configured\n      const precision = this.config.decimalPrecision || -1;\n      const formatNumber = (num) => {\n        if (num === null || !isFinite(num)) return num;\n        return precision >= 0 ? parseFloat(num.toFixed(precision)) : num;\n      };\n      \n      return {\n        outputs: {\n          add: formatNumber(add),\n          subtract: formatNumber(subtract),\n          multiply: formatNumber(multiply),\n          divide: formatNumber(divide)\n        }\n      };\n    } catch (error) {\n      throw new Error(`Math basic operation failed: ${error.message}`);\n    }\n  }\n  \n  updateConfig(newConfig) {\n    this.config = { ...this.config, ...newConfig };\n  }\n}", "dependencies": []}, "properties": {"panel": [{"type": "number", "name": "decimalPrecision", "label": "Decimal Precision", "defaultValue": 2, "validation": {"min": 0, "max": 10}, "description": "Number of decimal places in results (-1 for no rounding)"}, {"type": "select", "name": "divisionByZeroHandling", "label": "Division by Zero", "defaultValue": "null", "options": [{"value": "null", "label": "Return null"}, {"value": "infinity", "label": "Return Infinity"}, {"value": "error", "label": "<PERSON>hr<PERSON>r"}], "description": "How to handle division by zero"}]}}}