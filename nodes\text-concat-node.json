{"id": "text-concat-node", "name": "Text Concatenation", "type": "transform", "version": "1.0.0", "description": "Concatenates multiple text strings with optional separator", "metadata": {"category": "Text Processing", "tags": ["text", "string", "concatenation", "join"], "author": "Dynamic Workflow System", "documentation": "Combines multiple text inputs into a single output string with configurable separator"}, "pins": {"input": {"count": 3, "dynamic": true, "min": 2, "max": 10, "definitions": [{"name": "text1", "label": "Text 1", "dataType": "string", "required": true, "description": "First text string to concatenate"}, {"name": "text2", "label": "Text 2", "dataType": "string", "required": true, "description": "Second text string to concatenate"}, {"name": "separator", "label": "Separator", "dataType": "string", "required": false, "description": "Optional separator between text strings"}]}, "output": {"count": 1, "dynamic": false, "definitions": [{"name": "result", "label": "Concatenated Text", "dataType": "string", "description": "The combined text result"}]}}, "visual": {"icon": {"type": "unicode", "value": "🔗", "color": "#3F51B5"}, "shape": {"type": "rectangle"}, "sizing": {"baseWidth": 140, "baseHeight": 80, "pinSpacing": 20, "dynamicResize": true}, "colors": {"background": "#E8EAF6", "border": "#3F51B5", "text": "#1A237E"}}, "backend": {"endpoint": "/api/nodes/text-concat", "method": "POST", "code": "async function execute(req, res) {\n  try {\n    const { inputs, config } = req.body;\n    \n    // Get separator from input or config\n    const separator = String(inputs.separator !== undefined ? inputs.separator : (config.defaultSeparator || ''));\n    \n    // Collect all text inputs (text1, text2, text3, etc.)\n    const textInputs = [];\n    Object.keys(inputs).forEach(key => {\n      if (key.startsWith('text') && key !== 'separator') {\n        let text = String(inputs[key] || '');\n        \n        // Apply trimming if configured\n        if (config.trimInputs) {\n          text = text.trim();\n        }\n        \n        // Apply case handling if configured\n        if (config.caseHandling === 'lowercase') {\n          text = text.toLowerCase();\n        } else if (config.caseHandling === 'uppercase') {\n          text = text.toUpperCase();\n        }\n        \n        textInputs.push(text);\n      }\n    });\n    \n    // Concatenate all text inputs with separator\n    const result = textInputs.join(separator);\n    \n    res.json({\n      success: true,\n      outputs: {\n        result: result\n      },\n      metadata: {\n        executionTime: Date.now(),\n        inputCount: textInputs.length,\n        separator: separator,\n        appliedConfig: {\n          trimInputs: config.trimInputs,\n          caseHandling: config.caseHandling\n        }\n      }\n    });\n  } catch (error) {\n    console.error('Text concatenation error:', error);\n    res.status(500).json({\n      success: false,\n      error: error.message\n    });\n  }\n}", "dependencies": []}, "frontend": {"executable": {"code": "class TextConcatNode {\n  constructor(nodeData, eventEmitter) {\n    this.nodeData = nodeData;\n    this.eventEmitter = eventEmitter;\n    this.config = nodeData.config || {};\n  }\n  \n  async execute(inputs) {\n    try {\n      // Get separator from input or config\n      const separator = String(inputs.separator !== undefined ? inputs.separator : (this.config.defaultSeparator || ''));\n      \n      // Collect all text inputs (text1, text2, text3, etc.)\n      const textInputs = [];\n      Object.keys(inputs).forEach(key => {\n        if (key.startsWith('text') && key !== 'separator') {\n          let text = String(inputs[key] || '');\n          \n          // Apply trimming if configured\n          if (this.config.trimInputs) {\n            text = text.trim();\n          }\n          \n          // Apply case handling if configured\n          if (this.config.caseHandling === 'lowercase') {\n            text = text.toLowerCase();\n          } else if (this.config.caseHandling === 'uppercase') {\n            text = text.toUpperCase();\n          }\n          \n          textInputs.push(text);\n        }\n      });\n      \n      // Concatenate all text inputs with separator\n      const result = textInputs.join(separator);\n      \n      return {\n        outputs: {\n          result: result\n        }\n      };\n    } catch (error) {\n      throw new Error(`Text concatenation failed: ${error.message}`);\n    }\n  }\n  \n  updateConfig(newConfig) {\n    this.config = { ...this.config, ...newConfig };\n  }\n}", "dependencies": []}, "properties": {"panel": [{"type": "text", "name": "defaultSeparator", "label": "<PERSON><PERSON><PERSON>", "defaultValue": "", "validation": {"required": false}, "description": "Default separator to use when separator input is not connected"}, {"type": "checkbox", "name": "trimInputs", "label": "Trim Input Strings", "defaultValue": false, "description": "Remove leading and trailing whitespace from input strings"}, {"type": "select", "name": "caseHandling", "label": "Case Handling", "defaultValue": "preserve", "options": [{"value": "preserve", "label": "Preserve Original"}, {"value": "lowercase", "label": "Convert to Lowercase"}, {"value": "uppercase", "label": "Convert to Uppercase"}], "description": "How to handle text case during concatenation"}]}}}