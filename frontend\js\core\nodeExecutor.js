/**
 * Node Executor for Dynamic Workflow System
 * Handles the execution of individual nodes
 */

class NodeExecutor {
  constructor(nodeData, eventEmitter) {
    this.nodeData = nodeData;
    this.eventEmitter = eventEmitter;
    this.config = nodeData.config || {};
    this.status = 'idle';
    this.lastResult = null;
    
    // Extract execution code from node definition
    this.frontendCode = this.extractFrontendCode();
    this.backendCode = this.extractBackendCode();
  }

  /**
   * Execute the node with given input data
   */
  async execute(inputData = {}) {
    try {
      this.setStatus('running');
      
      console.log(`🔄 Executing node ${this.nodeData.id} with inputs:`, inputData);
      
      // Prepare execution context
      const context = this.createExecutionContext(inputData);
      
      // Execute based on node type
      let result;
      if (this.frontendCode) {
        result = await this.executeFrontendCode(context);
      } else if (this.backendCode) {
        result = await this.executeBackendCode(context);
      } else {
        result = await this.executeDefaultLogic(context);
      }
      
      this.lastResult = result;
      this.setStatus('completed');
      
      console.log(`✅ Node ${this.nodeData.id} execution completed:`, result);
      return result;
      
    } catch (error) {
      this.setStatus('error');
      console.error(`❌ Node ${this.nodeData.id} execution failed:`, error);
      throw error;
    }
  }

  /**
   * Create execution context for the node
   */
  createExecutionContext(inputData) {
    return {
      inputs: inputData,
      config: this.config,
      nodeId: this.nodeData.id,
      nodeType: this.nodeData.type,
      console: {
        log: (...args) => console.log(`[${this.nodeData.id}]`, ...args),
        error: (...args) => console.error(`[${this.nodeData.id}]`, ...args),
        warn: (...args) => console.warn(`[${this.nodeData.id}]`, ...args)
      },
      // Safe globals
      Math,
      Date,
      JSON,
      Array,
      Object,
      String,
      Number,
      Boolean
    };
  }

  /**
   * Execute frontend JavaScript code
   */
  async executeFrontendCode(context) {
    if (!this.frontendCode) {
      throw new Error('No frontend code available');
    }

    try {
      // Create a safe execution function
      const executeFunction = new Function(
        'inputs', 'config', 'console', 'Math', 'Date', 'JSON', 'Array', 'Object', 'String', 'Number', 'Boolean',
        `
        ${this.frontendCode}
        
        // Ensure we return a result with outputs
        if (typeof result === 'undefined') {
          result = inputs;
        }
        
        // Wrap result in outputs structure if needed
        if (result && typeof result === 'object' && !result.outputs) {
          return { outputs: { output1: result } };
        } else if (result && result.outputs) {
          return result;
        } else {
          return { outputs: { output1: result } };
        }
        `
      );

      const result = executeFunction(
        context.inputs,
        context.config,
        context.console,
        context.Math,
        context.Date,
        context.JSON,
        context.Array,
        context.Object,
        context.String,
        context.Number,
        context.Boolean
      );

      return result;
    } catch (error) {
      throw new Error(`Frontend code execution failed: ${error.message}`);
    }
  }

  /**
   * Execute backend code (simplified for frontend execution)
   */
  async executeBackendCode(context) {
    // For now, we'll execute backend code in the frontend
    // In a full implementation, this would make an API call
    console.warn('Backend code execution in frontend (simplified)');
    
    try {
      // Extract the actual execution logic from backend code
      const codeMatch = this.backendCode.match(/async function execute\(req, res\) \{([\s\S]*)\}/);
      if (!codeMatch) {
        throw new Error('Invalid backend code format');
      }
      
      // Simulate backend execution
      const mockReq = {
        body: {
          inputs: context.inputs,
          config: context.config
        }
      };
      
      let responseData = null;
      const mockRes = {
        json: (data) => { responseData = data; },
        status: () => mockRes
      };
      
      // Execute the backend function
      const executeFunction = new Function(
        'req', 'res', 'console', 'Math', 'Date', 'JSON',
        codeMatch[1]
      );
      
      await executeFunction(mockReq, mockRes, context.console, context.Math, context.Date, context.JSON);
      
      if (responseData && responseData.success && responseData.outputs) {
        return { outputs: responseData.outputs };
      } else if (responseData && responseData.error) {
        throw new Error(responseData.error);
      } else {
        return { outputs: { output1: responseData } };
      }
      
    } catch (error) {
      throw new Error(`Backend code execution failed: ${error.message}`);
    }
  }

  /**
   * Execute default logic for nodes without custom code
   */
  async executeDefaultLogic(context) {
    // Default behavior: pass through input data
    const outputs = {};
    
    // Map inputs to outputs based on node pin definitions
    if (this.nodeData.pins && this.nodeData.pins.output && this.nodeData.pins.output.definitions) {
      const outputPins = this.nodeData.pins.output.definitions;
      const inputKeys = Object.keys(context.inputs);
      
      outputPins.forEach((pin, index) => {
        const inputKey = inputKeys[index] || inputKeys[0];
        outputs[pin.name] = context.inputs[inputKey] || null;
      });
    } else {
      // Fallback: create a single output
      outputs.output1 = context.inputs.input1 || context.inputs;
    }
    
    return { outputs };
  }

  /**
   * Extract frontend executable code from node definition
   */
  extractFrontendCode() {
    if (this.nodeData.frontend && 
        this.nodeData.frontend.executable && 
        this.nodeData.frontend.executable.code) {
      return this.nodeData.frontend.executable.code;
    }
    return null;
  }

  /**
   * Extract backend code from node definition
   */
  extractBackendCode() {
    if (this.nodeData.backend && this.nodeData.backend.code) {
      return this.nodeData.backend.code;
    }
    return null;
  }

  /**
   * Update node configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log(`Updated config for node ${this.nodeData.id}:`, this.config);
  }

  /**
   * Set node execution status
   */
  setStatus(status) {
    this.status = status;
    this.eventEmitter.emit('node:status:changed', {
      nodeId: this.nodeData.id,
      status: status
    });
  }

  /**
   * Get current status
   */
  getStatus() {
    return this.status;
  }

  /**
   * Get last execution result
   */
  getLastResult() {
    return this.lastResult;
  }
}
