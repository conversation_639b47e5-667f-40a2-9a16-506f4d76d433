<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Workflow System - 增强版 v1.0 (连接系统)</title>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            overflow: hidden;
        }
        
        .app-container {
            display: flex;
            height: 100vh;
            flex-direction: column;
        }
        
        /* Header */
        .app-header {
            height: 60px;
            background: white;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .app-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .version-badge {
            background: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 500;
        }
        
        .header-controls {
            margin-left: auto;
            display: flex;
            gap: 10px;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            background: #d4edda;
            border-radius: 20px;
            font-size: 12px;
            color: #155724;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
        }
        
        /* Main Content */
        .app-main {
            flex: 1;
            display: flex;
            height: calc(100vh - 60px); /* 减去header高度 */
        }
        
        /* Sidebar */
        .sidebar {
            width: 280px;
            background: white;
            border-right: 1px solid #e1e5e9;
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e1e5e9;
            background: #f8f9fa;
        }
        
        .sidebar-header h3 {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .search-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e1e5e9;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .sidebar-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .node-category {
            margin-bottom: 20px;
        }

        .category-title {
            font-size: 14px;
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .category-title:hover {
            color: #495057;
        }

        .category-toggle {
            font-size: 12px;
            transition: transform 0.2s ease;
        }

        .category-toggle.collapsed {
            transform: rotate(-90deg);
        }

        .category-content {
            transition: max-height 0.3s ease, opacity 0.3s ease;
            overflow: hidden;
        }

        .category-content.collapsed {
            max-height: 0;
            opacity: 0;
        }
        
        .node-item {
            padding: 12px;
            margin: 6px 0;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            cursor: move;
            user-select: none;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.2s ease;
        }
        
        .node-item:hover {
            background: #e3f2fd;
            border-color: #2196f3;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);
        }
        
        .node-item.dragging {
            opacity: 0.6;
            transform: rotate(5deg);
        }
        
        .node-icon {
            font-size: 18px;
            width: 24px;
            text-align: center;
        }
        
        .node-info {
            flex: 1;
        }
        
        .node-name {
            font-weight: 500;
            color: #2c3e50;
            font-size: 14px;
        }
        
        .node-description {
            font-size: 12px;
            color: #6c757d;
            margin-top: 2px;
        }
        
        /* Canvas Area */
        .canvas-section {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .canvas-toolbar {
            height: 50px;
            background: white;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            align-items: center;
            padding: 0 20px;
            gap: 10px;
        }
        
        .btn {
            padding: 6px 12px;
            border: 1px solid #e1e5e9;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s ease;
        }
        
        .btn:hover {
            background: #f8f9fa;
            border-color: #2196f3;
            color: #2196f3;
        }
        
        .btn.active {
            background: #2196f3;
            color: white;
            border-color: #2196f3;
        }
        
        .zoom-info {
            margin-left: auto;
            font-size: 12px;
            color: #6c757d;
            padding: 6px 12px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .canvas-container {
            flex: 1;
            position: relative;
            background: #e8e8e8;
            overflow: hidden;
        }
        
        .canvas {
            /* 增大画布区域5倍 */
            width: 500%;
            height: 500%;
            position: relative;
            cursor: grab;
            transform-origin: 0 0;
            /* 添加明显的边界框 */
            border: 3px solid #2196f3;
            box-shadow:
                0 0 0 1px #e3f2fd,
                0 0 20px rgba(33, 150, 243, 0.3);
            background: #fafbfc;
        }

        .canvas:active {
            cursor: grabbing;
        }

        /* 画布边界指示器 */
        .canvas::before {
            content: '画布边界';
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(33, 150, 243, 0.9);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            z-index: 1000;
            pointer-events: none;
        }

        .canvas::after {
            content: '拖拽节点到此区域';
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(33, 150, 243, 0.9);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            z-index: 1000;
            pointer-events: none;
        }
        
        .canvas:active {
            cursor: grabbing;
        }
        
        .grid-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.4;
            background-image:
                linear-gradient(to right, #e1e5e9 1px, transparent 1px),
                linear-gradient(to bottom, #e1e5e9 1px, transparent 1px);
            background-size: 20px 20px;
            pointer-events: none;
        }
        
        /* SVG连接层 */
        .connections-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            /* 确保变换原点与画布一致 */
            transform-origin: 0 0;
        }
        
        .connection-line {
            fill: none;
            stroke: #2196f3;
            stroke-width: 2;
            pointer-events: stroke;
            cursor: pointer;
        }
        
        .connection-line:hover {
            stroke: #ff9800;
            stroke-width: 3;
        }
        
        .connection-line.selected {
            stroke: #f44336;
            stroke-width: 3;
        }
        
        .temp-connection {
            stroke: #ff9800 !important;
            stroke-dasharray: 5,5 !important;
            stroke-width: 2 !important;
            fill: none !important;
            pointer-events: none !important;
        }
        
        .workflow-node {
            position: absolute;
            min-width: 200px;
            min-height: 120px;
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            cursor: move;
            user-select: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            /* 移除transition，避免拖拽延迟和连线脱节 */
            z-index: 2;
            /* 改为相对定位以支持引脚绝对定位 */
        }
        
        .workflow-node:hover {
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        
        .workflow-node.dragging {
            opacity: 0.9;
            z-index: 1000;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            /* 移除旋转和缩放，避免连线脱节 */
        }
        
        .workflow-node.selected {
            border-color: #ff9800;
            box-shadow: 0 0 0 3px rgba(255, 152, 0, 0.2);
        }
        
        .node-header {
            padding: 12px 16px;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            align-items: center;
            gap: 10px;
            background: #f8f9fa;
            border-radius: 10px 10px 0 0;
        }
        
        .node-title {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
            flex: 1;
        }
        
        .node-status-lights {
            margin-left: auto;
            display: flex;
            gap: 6px;
            align-items: center;
        }

        .status-light {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 1px solid rgba(0,0,0,0.2);
            position: relative;
            cursor: help;
        }

        /* 执行状态灯 */
        .execution-status[data-status="running"] {
            background: #4caf50; /* 绿色 - 执行中 */
            box-shadow: 0 0 6px rgba(76, 175, 80, 0.6);
        }

        .execution-status[data-status="waiting"] {
            background: #ff9800; /* 黄色 - 等待输入 */
            box-shadow: 0 0 6px rgba(255, 152, 0, 0.6);
        }

        .execution-status[data-status="error"] {
            background: #f44336; /* 红色 - 运行错误 */
            box-shadow: 0 0 6px rgba(244, 67, 54, 0.6);
        }

        /* 配置状态灯 */
        .config-status[data-status="complete"] {
            background: #4caf50; /* 绿色 - 配置完善 */
            box-shadow: 0 0 6px rgba(76, 175, 80, 0.6);
        }

        .config-status[data-status="incomplete"] {
            background: #ff9800; /* 黄色 - 配置未完善 */
            box-shadow: 0 0 6px rgba(255, 152, 0, 0.6);
        }

        .config-status[data-status="error"] {
            background: #f44336; /* 红色 - 配置错误 */
            box-shadow: 0 0 6px rgba(244, 67, 54, 0.6);
        }
        
        .node-body {
            padding: 16px;
            flex: 1;
            position: relative;
            /* 为引脚提供定位上下文 */
        }

        .node-content {
            text-align: center;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .node-description {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        /* Pin样式 - 重新设计为边缘布局 */
        .node-pins {
            position: absolute;
            display: flex;
            flex-direction: column;
            gap: 20px; /* 固定引脚间距 */
        }

        .node-pins.input {
            left: -8px; /* 引脚伸出到节点边缘外 */
            top: 45px; /* 从图标下方开始（更靠近顶部） */
        }

        .node-pins.output {
            right: -8px; /* 引脚伸出到节点边缘外 */
            top: 45px; /* 从图标下方开始（更靠近顶部） */
        }
        
        .pin {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid #6c757d;
            background: white;
            cursor: pointer;
            position: relative;
            transition: all 0.2s ease;
            z-index: 10; /* 确保引脚在最上层 */
        }

        .pin:hover {
            transform: scale(1.2);
            border-color: #2196f3;
            background: #e3f2fd;
        }

        .pin.connected {
            background: #2196f3;
            border-color: #2196f3;
        }

        .pin-label {
            font-size: 11px;
            color: #6c757d;
            white-space: nowrap;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            pointer-events: none; /* 标签不阻挡点击 */
        }

        .pin-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        .pin-container.input .pin-label {
            left: 24px; /* 标签在引脚右侧（节点内侧） */
        }

        .pin-container.output .pin-label {
            right: 24px; /* 标签在引脚左侧（节点内侧） */
        }
        
        /* Properties Panel */
        .properties-panel {
            width: 300px;
            background: white;
            border-left: 1px solid #e1e5e9;
            display: flex;
            flex-direction: column;
        }
        
        .properties-header {
            padding: 20px;
            border-bottom: 1px solid #e1e5e9;
            background: #f8f9fa;
        }
        
        .properties-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .no-selection {
            text-align: center;
            color: #6c757d;
            padding: 40px 20px;
        }
        
        .no-selection i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        /* Footer */
        .app-footer {
            height: 40px;
            background: white;
            border-top: 1px solid #e1e5e9;
            display: flex;
            align-items: center;
            padding: 0 20px;
            font-size: 12px;
            color: #6c757d;
        }
        
        .footer-info {
            display: flex;
            gap: 20px;
        }
        
        .footer-right {
            margin-left: auto;
        }
        
        /* 连接创建提示 */
        .connection-hint {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .connection-hint.show {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <h1 class="app-title">
                <i class="fas fa-project-diagram"></i>
                Dynamic Workflow System
                <span class="version-badge">v5.1 修复状态灯CSP</span>
            </h1>
            
            <div class="header-controls">
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span>明确边界 + 精确拖拽 + 完美缩放</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <!-- Left Sidebar - Node Palette -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <h3>节点调色板</h3>
                    <input type="text" class="search-input" placeholder="搜索节点..." id="node-search">
                </div>

                <div class="sidebar-content" id="node-palette">
                    <!-- 节点将在这里动态加载 -->
                </div>
            </aside>

            <!-- Central Canvas -->
            <section class="canvas-section">
                <div class="canvas-toolbar">
                    <button class="btn" onclick="zoomIn()">
                        <i class="fas fa-search-plus"></i>
                        放大
                    </button>
                    <button class="btn" onclick="zoomOut()">
                        <i class="fas fa-search-minus"></i>
                        缩小
                    </button>
                    <button class="btn" onclick="resetView()">
                        <i class="fas fa-expand-arrows-alt"></i>
                        重置
                    </button>
                    <button class="btn" onclick="clearCanvas()">
                        <i class="fas fa-trash"></i>
                        清空
                    </button>
                    <button class="btn active" onclick="toggleGrid()" id="grid-btn">
                        <i class="fas fa-th"></i>
                        网格
                    </button>
                    <button class="btn" onclick="toggleConnectionMode()" id="connection-btn">
                        <i class="fas fa-link"></i>
                        连接模式
                    </button>
                    <button class="btn" onclick="executeWorkflowDemo()" style="background: #28a745; color: white;">
                        <i class="fas fa-play"></i>
                        执行工作流
                    </button>
                    <button class="btn" onclick="testZoomFunction()" style="background: #ff9800; color: white;">
                        <i class="fas fa-vial"></i>
                        测试缩放
                    </button>
                    
                    <div class="zoom-info">
                        缩放: <span id="zoom-display">100%</span> |
                        节点: <span id="node-count">0</span> |
                        连接: <span id="connection-count">0</span> |
                        平移: <span id="pan-display">0, 0</span>
                    </div>
                </div>

                <div class="canvas-container">
                    <div class="grid-background" id="grid"></div>
                    
                    <!-- SVG连接层 -->
                    <svg class="connections-svg" id="connections-svg">
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                                    refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#2196f3" />
                            </marker>
                        </defs>
                        <g id="connections-group"></g>
                    </svg>
                    
                    <div class="canvas" id="canvas">
                        <!-- 工作流节点将在这里创建 -->
                    </div>
                    
                    <!-- 连接提示 -->
                    <div class="connection-hint" id="connection-hint">
                        点击输出Pin，然后点击目标输入Pin来创建连接
                    </div>
                </div>
            </section>

            <!-- Right Sidebar - Properties Panel -->
            <aside class="properties-panel">
                <div class="properties-header">
                    <h3>属性面板</h3>
                </div>

                <div class="properties-content" id="properties">
                    <div class="no-selection">
                        <i class="fas fa-mouse-pointer"></i>
                        <p>选择一个节点或连接来查看其属性</p>
                    </div>
                </div>
            </aside>
        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-info">
                <span>第一阶段: 连接系统</span>
                <span>新增: 无限画布 + AutoCAD式缩放 + 鼠标中心缩放</span>
            </div>
            
            <div class="footer-right">
                <span>Dynamic Workflow System v1.0 (增强版)</span>
            </div>
        </footer>
    </div>

    <script>
        console.log('🚀 Dynamic Workflow System v1.0 - 连接系统启动');
        
        // 全局状态
        let zoom = 1;
        let pan = { x: 0, y: 0 };
        let nodeCounter = 0;
        let connectionCounter = 0;
        let selectedNode = null;
        let selectedConnection = null;
        let isDraggingCanvas = false;
        let isDraggingNode = false;
        let isConnecting = false;
        let connectionMode = false;
        let dragStart = { x: 0, y: 0 };
        let showGrid = true;
        let nodes = [];
        let connections = [];
        let sourcePin = null;
        let tempConnection = null;

        // 应用状态管理
        const AppState = {
            nodes: new Map(), // 存储节点数据，key为nodeId，value为节点数据对象
            connections: new Map() // 存储连接数据
        };

        // 存储从API加载的完整节点定义
        let apiNodeDefinitions = new Map();

        const canvas = document.getElementById('canvas');
        const grid = document.getElementById('grid');
        const connectionsSvg = document.getElementById('connections-svg');
        const connectionsGroup = document.getElementById('connections-group');
        
        // 节点类型配置（增强版，包含Pin定义）
        const nodeTypes = {
            'conditional-node': { 
                icon: '🔀', 
                name: '条件节点', 
                description: '基于条件进行分支',
                color: '#4caf50',
                category: '控制流',
                pins: {
                    input: [
                        { name: 'condition', label: '条件', type: 'boolean' },
                        { name: 'input', label: '输入', type: 'any' }
                    ],
                    output: [
                        { name: 'true', label: '真', type: 'any' },
                        { name: 'false', label: '假', type: 'any' }
                    ]
                }
            },
            'loop-node': { 
                icon: '🔄', 
                name: '循环节点', 
                description: '重复执行操作',
                color: '#ff9800',
                category: '控制流',
                pins: {
                    input: [
                        { name: 'input', label: '输入', type: 'any' },
                        { name: 'count', label: '次数', type: 'number' }
                    ],
                    output: [
                        { name: 'output', label: '输出', type: 'any' },
                        { name: 'index', label: '索引', type: 'number' }
                    ]
                }
            },
            'custom-task-node': { 
                icon: '🔧', 
                name: '任务节点', 
                description: '执行自定义任务',
                color: '#2196f3',
                category: '任务',
                pins: {
                    input: [
                        { name: 'input', label: '输入', type: 'any' }
                    ],
                    output: [
                        { name: 'output', label: '输出', type: 'any' }
                    ]
                }
            },
            'multi-branch-node': { 
                icon: '🌿', 
                name: '多分支节点', 
                description: '多路径分发',
                color: '#9c27b0',
                category: '控制流',
                pins: {
                    input: [
                        { name: 'input', label: '输入', type: 'any' }
                    ],
                    output: [
                        { name: 'output1', label: '输出1', type: 'any' },
                        { name: 'output2', label: '输出2', type: 'any' },
                        { name: 'output3', label: '输出3', type: 'any' }
                    ]
                }
            },
            'input-node': { 
                icon: '📥', 
                name: '输入节点', 
                description: '数据输入',
                color: '#607d8b',
                category: '数据',
                pins: {
                    input: [],
                    output: [
                        { name: 'value', label: '值', type: 'any' }
                    ]
                }
            },
            'output-node': {
                icon: '📤',
                name: '输出节点',
                description: '数据输出',
                color: '#f44336',
                category: '数据',
                pins: {
                    input: [
                        { name: 'input', label: '输入', type: 'any' }
                    ],
                    output: []
                }
            },
            'text-concat-node': {
                icon: '🔗',
                name: '文本连接',
                description: '连接多个文本字符串',
                color: '#3f51b5',
                category: '文本处理',
                pins: {
                    input: [
                        { name: 'text1', label: '文本1', type: 'string' },
                        { name: 'text2', label: '文本2', type: 'string' },
                        { name: 'separator', label: '分隔符', type: 'string' }
                    ],
                    output: [
                        { name: 'result', label: '结果', type: 'string' }
                    ]
                }
            },
            'text-split-node': {
                icon: '✂️',
                name: '文本分割',
                description: '按分隔符分割文本',
                color: '#3f51b5',
                category: '文本处理',
                pins: {
                    input: [
                        { name: 'text', label: '文本', type: 'string' },
                        { name: 'separator', label: '分隔符', type: 'string' }
                    ],
                    output: [
                        { name: 'parts', label: '分割结果', type: 'array' },
                        { name: 'count', label: '数量', type: 'number' }
                    ]
                }
            },
            'text-replace-node': {
                icon: '🔄',
                name: '文本替换',
                description: '替换文本中的内容',
                color: '#3f51b5',
                category: '文本处理',
                pins: {
                    input: [
                        { name: 'text', label: '原文本', type: 'string' },
                        { name: 'search', label: '查找', type: 'string' },
                        { name: 'replace', label: '替换', type: 'string' }
                    ],
                    output: [
                        { name: 'result', label: '结果', type: 'string' }
                    ]
                }
            },
            'text-case-node': {
                icon: '🔤',
                name: '大小写转换',
                description: '转换文本大小写',
                color: '#3f51b5',
                category: '文本处理',
                pins: {
                    input: [
                        { name: 'text', label: '文本', type: 'string' }
                    ],
                    output: [
                        { name: 'uppercase', label: '大写', type: 'string' },
                        { name: 'lowercase', label: '小写', type: 'string' },
                        { name: 'capitalize', label: '首字母大写', type: 'string' }
                    ]
                }
            },
            'text-length-node': {
                icon: '📏',
                name: '文本长度',
                description: '计算文本长度和统计信息',
                color: '#3f51b5',
                category: '文本处理',
                pins: {
                    input: [
                        { name: 'text', label: '文本', type: 'string' }
                    ],
                    output: [
                        { name: 'length', label: '字符数', type: 'number' },
                        { name: 'words', label: '单词数', type: 'number' },
                        { name: 'lines', label: '行数', type: 'number' }
                    ]
                }
            },
            'manual-input-node': {
                icon: '✏️',
                name: '手动输入',
                description: '手动输入文本或数值数据',
                color: '#607d8b',
                category: '数据输入',
                pins: {
                    input: [],
                    output: [
                        { name: 'text', label: '文本', type: 'string' },
                        { name: 'number', label: '数值', type: 'number' }
                    ]
                }
            },
            'constant-node': {
                icon: '📌',
                name: '常量值',
                description: '输出预设的常量值',
                color: '#607d8b',
                category: '数据输入',
                pins: {
                    input: [],
                    output: [
                        { name: 'value', label: '值', type: 'any' }
                    ]
                }
            },
            'random-data-node': {
                icon: '🎲',
                name: '随机数据',
                description: '生成随机数据用于测试',
                color: '#607d8b',
                category: '数据输入',
                pins: {
                    input: [],
                    output: [
                        { name: 'number', label: '随机数', type: 'number' },
                        { name: 'text', label: '随机文本', type: 'string' },
                        { name: 'boolean', label: '随机布尔', type: 'boolean' }
                    ]
                }
            },
            'counter-node': {
                icon: '🔢',
                name: '计数器',
                description: '生成递增的数字序列',
                color: '#607d8b',
                category: '数据输入',
                pins: {
                    input: [
                        { name: 'trigger', label: '触发', type: 'any' }
                    ],
                    output: [
                        { name: 'count', label: '计数', type: 'number' },
                        { name: 'isFirst', label: '是否首次', type: 'boolean' }
                    ]
                }
            },
            'math-basic-node': {
                icon: '➕',
                name: '基础运算',
                description: '加减乘除基础数学运算',
                color: '#ff5722',
                category: '数学计算',
                pins: {
                    input: [
                        { name: 'a', label: '数值A', type: 'number' },
                        { name: 'b', label: '数值B', type: 'number' }
                    ],
                    output: [
                        { name: 'add', label: '加法', type: 'number' },
                        { name: 'subtract', label: '减法', type: 'number' },
                        { name: 'multiply', label: '乘法', type: 'number' },
                        { name: 'divide', label: '除法', type: 'number' }
                    ]
                }
            },
            'math-functions-node': {
                icon: '📐',
                name: '数学函数',
                description: '三角函数、对数、指数等数学函数',
                color: '#ff5722',
                category: '数学计算',
                pins: {
                    input: [
                        { name: 'value', label: '输入值', type: 'number' }
                    ],
                    output: [
                        { name: 'sin', label: '正弦', type: 'number' },
                        { name: 'cos', label: '余弦', type: 'number' },
                        { name: 'sqrt', label: '平方根', type: 'number' },
                        { name: 'abs', label: '绝对值', type: 'number' },
                        { name: 'log', label: '对数', type: 'number' }
                    ]
                }
            },
            'math-stats-node': {
                icon: '📊',
                name: '统计函数',
                description: '计算数组的统计信息',
                color: '#ff5722',
                category: '数学计算',
                pins: {
                    input: [
                        { name: 'numbers', label: '数字数组', type: 'array' }
                    ],
                    output: [
                        { name: 'sum', label: '总和', type: 'number' },
                        { name: 'average', label: '平均值', type: 'number' },
                        { name: 'min', label: '最小值', type: 'number' },
                        { name: 'max', label: '最大值', type: 'number' },
                        { name: 'count', label: '数量', type: 'number' }
                    ]
                }
            },
            'math-compare-node': {
                icon: '⚖️',
                name: '数值比较',
                description: '比较两个数值的大小关系',
                color: '#ff5722',
                category: '数学计算',
                pins: {
                    input: [
                        { name: 'a', label: '数值A', type: 'number' },
                        { name: 'b', label: '数值B', type: 'number' }
                    ],
                    output: [
                        { name: 'equal', label: '相等', type: 'boolean' },
                        { name: 'greater', label: 'A>B', type: 'boolean' },
                        { name: 'less', label: 'A<B', type: 'boolean' },
                        { name: 'difference', label: '差值', type: 'number' }
                    ]
                }
            }
        };
        
        // 初始化应用
        async function init() {
            console.log('🔄 初始化连接系统...');
            
            try {
                await loadNodes();
                setupEventListeners();
                renderNodePalette();
                updateDisplay();
                
                console.log('✅ 连接系统初始化完成');
                showConnectionHint();
            } catch (error) {
                console.error('❌ 初始化失败:', error);
            }
        }
        
        // 显示连接提示
        function showConnectionHint() {
            const hint = document.getElementById('connection-hint');
            hint.classList.add('show');
            setTimeout(() => {
                hint.classList.remove('show');
            }, 3000);
        }

        // 测试缩放功能
        function testZoomFunction() {
            console.log('🧪 测试缩放功能...');

            // 清除之前的测试标记
            document.querySelectorAll('.zoom-test-marker').forEach(el => el.remove());

            // 创建一个测试节点在画布中心
            const rect = canvas.getBoundingClientRect();
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            // 转换为画布坐标
            const canvasX = (centerX - pan.x) / zoom;
            const canvasY = (centerY - pan.y) / zoom;

            const testNode = createNode('custom-task-node', canvasX, canvasY);
            testNode.style.background = '#ff9800';
            testNode.style.border = '3px solid #f57c00';
            testNode.querySelector('.node-title').textContent = '缩放测试节点';
            testNode.classList.add('zoom-test-marker');

            // 创建十字标记在画布中心
            const crosshair = document.createElement('div');
            crosshair.className = 'zoom-test-marker';
            crosshair.style.cssText = `
                position: absolute;
                left: ${centerX - 10}px;
                top: ${centerY - 10}px;
                width: 20px;
                height: 20px;
                pointer-events: none;
                z-index: 1000;
                border: 2px solid red;
                background: rgba(255, 0, 0, 0.3);
            `;

            // 添加十字线
            const hLine = document.createElement('div');
            hLine.style.cssText = `
                position: absolute;
                left: -50px;
                top: 8px;
                width: 120px;
                height: 2px;
                background: red;
            `;

            const vLine = document.createElement('div');
            vLine.style.cssText = `
                position: absolute;
                left: 8px;
                top: -50px;
                width: 2px;
                height: 120px;
                background: red;
            `;

            crosshair.appendChild(hLine);
            crosshair.appendChild(vLine);
            document.querySelector('.canvas-container').appendChild(crosshair);

            console.log(`测试节点创建在画布坐标: (${canvasX.toFixed(1)}, ${canvasY.toFixed(1)})`);
            console.log('🎯 红色十字标记显示画布中心位置');
            console.log('📍 将鼠标放在红色十字上，然后滚动鼠标滚轮');
            console.log('✅ 如果缩放正确，十字应该始终保持在鼠标下方');
        }
        
        // 加载节点数据
        async function loadNodes() {
            try {
                const response = await fetch('/api/nodes');
                if (response.ok) {
                    const apiNodes = await response.json();
                    console.log(`📦 从API加载了 ${apiNodes.length} 个节点类型`);

                    // 合并API节点和默认节点
                    apiNodes.forEach(node => {
                        // 保存完整的节点定义
                        apiNodeDefinitions.set(node.id, node);

                        if (!nodeTypes[node.id] && node.pins) {
                            nodeTypes[node.id] = {
                                icon: node.visual?.icon?.value || '📦',
                                name: node.name,
                                description: node.description || '自定义节点',
                                color: node.visual?.icon?.color || '#666',
                                category: node.metadata?.category || '其他',
                                pins: node.pins
                            };
                        }
                    });
                } else {
                    console.warn('⚠️ API响应异常，使用默认节点');
                }
            } catch (error) {
                console.warn('⚠️ 无法连接API，使用默认节点:', error.message);
            }
        }

        // 渲染节点调色板
        function renderNodePalette() {
            const palette = document.getElementById('node-palette');
            palette.innerHTML = '';

            // 按类别分组
            const categories = {};
            Object.entries(nodeTypes).forEach(([id, config]) => {
                const category = config.category || '其他';
                if (!categories[category]) {
                    categories[category] = [];
                }
                categories[category].push({ id, ...config });
            });

            // 渲染每个类别
            Object.entries(categories).forEach(([categoryName, categoryNodes]) => {
                const categoryDiv = document.createElement('div');
                categoryDiv.className = 'node-category';

                const title = document.createElement('div');
                title.className = 'category-title';
                title.innerHTML = `
                    <span>${categoryName} (${categoryNodes.length})</span>
                    <span class="category-toggle">▼</span>
                `;

                const content = document.createElement('div');
                content.className = 'category-content';

                // 添加折叠功能
                title.addEventListener('click', () => {
                    const toggle = title.querySelector('.category-toggle');
                    const isCollapsed = content.classList.contains('collapsed');

                    if (isCollapsed) {
                        content.classList.remove('collapsed');
                        toggle.classList.remove('collapsed');
                        content.style.maxHeight = content.scrollHeight + 'px';
                    } else {
                        content.classList.add('collapsed');
                        toggle.classList.add('collapsed');
                        content.style.maxHeight = '0px';
                    }
                });

                categoryDiv.appendChild(title);

                categoryNodes.forEach(node => {
                    const item = document.createElement('div');
                    item.className = 'node-item';
                    item.draggable = true;
                    item.dataset.nodeType = node.id;

                    item.innerHTML = `
                        <div class="node-icon">${node.icon}</div>
                        <div class="node-info">
                            <div class="node-name">${node.name}</div>
                            <div class="node-description">${node.description}</div>
                        </div>
                    `;

                    // 拖拽事件
                    item.addEventListener('dragstart', (e) => {
                        e.dataTransfer.setData('text/node-type', node.id);
                        item.classList.add('dragging');
                    });

                    item.addEventListener('dragend', () => {
                        item.classList.remove('dragging');
                    });

                    content.appendChild(item);
                });

                // 设置初始高度
                content.style.maxHeight = content.scrollHeight + 'px';

                categoryDiv.appendChild(content);
                palette.appendChild(categoryDiv);
            });
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 获取画布容器元素
            const canvasContainer = document.querySelector('.canvas-container');

            // 画布拖放
            canvas.addEventListener('dragover', (e) => {
                e.preventDefault();
            });

            canvas.addEventListener('drop', (e) => {
                e.preventDefault();
                const nodeType = e.dataTransfer.getData('text/node-type');
                if (nodeType) {
                    // 获取画布容器的位置，而不是canvas元素的位置
                    const canvasContainer = document.querySelector('.canvas-container');
                    const rect = canvasContainer.getBoundingClientRect();

                    // 计算鼠标在画布坐标系中的位置
                    const x = (e.clientX - rect.left - pan.x) / zoom;
                    const y = (e.clientY - rect.top - pan.y) / zoom;

                    createNode(nodeType, x, y);
                    console.log(`节点创建在: (${x.toFixed(1)}, ${y.toFixed(1)}), 鼠标位置: (${e.clientX}, ${e.clientY})`);
                }
            });

            // 画布平移（恢复原始算法）
            canvas.addEventListener('mousedown', (e) => {
                if (e.target === canvas || e.target === grid) {
                    isDraggingCanvas = true;
                    dragStart = { x: e.clientX - pan.x, y: e.clientY - pan.y };
                    canvas.style.cursor = 'grabbing';
                }
            });

            document.addEventListener('mousemove', (e) => {
                if (isDraggingCanvas) {
                    pan.x = e.clientX - dragStart.x;
                    pan.y = e.clientY - dragStart.y;
                    updateTransform();
                    updateDisplay();
                } else if (isConnecting && sourcePin) {
                    updateTempConnection(e);
                }
            });

            document.addEventListener('mouseup', () => {
                isDraggingCanvas = false;
                canvas.style.cursor = 'grab';
            });

            // 键盘事件处理
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    if (isConnecting) {
                        console.log('Escape键取消连接');
                        cancelConnection();
                    }
                    clearSelection();
                } else if (e.key === 'Delete') {
                    if (selectedConnection) {
                        deleteConnection(selectedConnection);
                    } else if (selectedNode) {
                        deleteNode(selectedNode);
                    }
                } else if (e.ctrlKey && e.key === 'c') {
                    e.preventDefault();
                    toggleConnectionMode();
                }
            });

            // 缩放（以鼠标为中心，类似AutoCAD）
            // 在整个画布容器上监听，确保任何位置都能缩放
            canvasContainer.addEventListener('wheel', (e) => {
                e.preventDefault();
                e.stopPropagation();

                try {
                    // 计算缩放因子
                    const scaleFactor = e.deltaY > 0 ? 0.9 : 1.1;
                    const oldZoom = zoom;
                    const newZoom = Math.max(0.1, Math.min(5, zoom * scaleFactor));

                    // 如果缩放没有变化，直接返回
                    if (Math.abs(newZoom - oldZoom) < 0.001) {
                        console.log('缩放已达到极限');
                        return;
                    }

                    // 获取画布容器的边界矩形
                    const canvasContainer = document.querySelector('.canvas-container');
                    const rect = canvasContainer.getBoundingClientRect();

                    // 计算鼠标相对于画布容器的位置
                    const mouseX = e.clientX - rect.left;
                    const mouseY = e.clientY - rect.top;

                    // 验证鼠标位置是否有效
                    if (isNaN(mouseX) || isNaN(mouseY)) {
                        console.error('无效的鼠标位置');
                        return;
                    }

                    // 正确的鼠标中心缩放算法
                    // 1. 计算鼠标在当前变换下的世界坐标
                    const worldX = (mouseX - pan.x) / oldZoom;
                    const worldY = (mouseY - pan.y) / oldZoom;

                    // 2. 更新缩放级别
                    zoom = newZoom;

                    // 3. 重新计算平移，使世界坐标点在屏幕上的位置保持不变
                    pan.x = mouseX - worldX * newZoom;
                    pan.y = mouseY - worldY * newZoom;

                    // 验证计算结果
                    if (isNaN(pan.x) || isNaN(pan.y)) {
                        console.error('平移计算错误，恢复原值');
                        zoom = oldZoom;
                        return;
                    }

                    // 应用变换并更新显示
                    updateTransform();
                    updateDisplay();
                    updateAllConnections();

                    // 验证缩放是否正确：重新计算鼠标的世界坐标，应该与之前相同
                    const verifyWorldX = (mouseX - pan.x) / newZoom;
                    const verifyWorldY = (mouseY - pan.y) / newZoom;
                    const errorX = Math.abs(worldX - verifyWorldX);
                    const errorY = Math.abs(worldY - verifyWorldY);

                    // 调试信息
                    console.log(`✅ 缩放: ${oldZoom.toFixed(2)} → ${newZoom.toFixed(2)}, 鼠标: (${mouseX.toFixed(1)}, ${mouseY.toFixed(1)}), 平移: (${pan.x.toFixed(1)}, ${pan.y.toFixed(1)}), 误差: (${errorX.toFixed(3)}, ${errorY.toFixed(3)})`);

                } catch (error) {
                    console.error('缩放过程中发生错误:', error);
                }
            });

            // 键盘快捷键
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Delete') {
                    if (selectedNode) {
                        deleteNode(selectedNode);
                    } else if (selectedConnection) {
                        deleteConnection(selectedConnection);
                    }
                }
                if (e.key === 'Escape') {
                    clearSelection();
                    if (isConnecting) {
                        cancelConnection();
                    }
                }
                if (e.key === 'c' && e.ctrlKey) {
                    toggleConnectionMode();
                }
            });

            // 搜索功能
            document.getElementById('node-search').addEventListener('input', (e) => {
                filterNodes(e.target.value);
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);

        // 创建节点
        function createNode(type, x, y) {
            nodeCounter++;
            const config = nodeTypes[type];

            const node = document.createElement('div');
            node.className = 'workflow-node';
            node.dataset.nodeId = `${type}_${nodeCounter}`;
            node.dataset.nodeType = type;
            node.style.left = x + 'px';
            node.style.top = y + 'px';
            node.style.borderColor = config.color;

            // 初始化引脚数量
            node.dataset.inputPinCount = config.pins.input.length;
            node.dataset.outputPinCount = config.pins.output.length;

            // 计算初始节点尺寸
            const maxPins = Math.max(config.pins.input.length, config.pins.output.length);
            const { width, height } = calculateNodeSize(maxPins);

            node.style.width = width + 'px';
            node.style.height = height + 'px';

            // 创建节点结构
            const header = document.createElement('div');
            header.className = 'node-header';
            header.innerHTML = `
                <div class="node-icon" style="font-size: 16px;">${config.icon}</div>
                <div class="node-title">${config.name}</div>
                <div class="node-status-lights">
                    <div class="status-light execution-status" data-status="waiting" title="执行状态: 等待输入"></div>
                    <div class="status-light config-status" data-status="incomplete" title="配置状态: 未完善"></div>
                </div>
            `;

            const body = document.createElement('div');
            body.className = 'node-body';

            // 创建节点内容区域
            const content = document.createElement('div');
            content.className = 'node-content';
            content.innerHTML = `
                <div class="node-description">${config.description || ''}</div>
            `;

            // 创建输入Pin
            const inputPins = document.createElement('div');
            inputPins.className = 'node-pins input';
            config.pins.input.forEach((pinConfig, index) => {
                const pinContainer = createPin(pinConfig, 'input', node.dataset.nodeId, index);
                inputPins.appendChild(pinContainer);
            });

            // 创建输出Pin
            const outputPins = document.createElement('div');
            outputPins.className = 'node-pins output';
            config.pins.output.forEach((pinConfig, index) => {
                const pinContainer = createPin(pinConfig, 'output', node.dataset.nodeId, index);
                outputPins.appendChild(pinContainer);
            });

            body.appendChild(content);
            body.appendChild(inputPins);
            body.appendChild(outputPins);

            node.appendChild(header);
            node.appendChild(body);

            // 节点事件
            node.addEventListener('mousedown', (e) => {
                if (e.target.classList.contains('pin')) return;
                e.stopPropagation();
                selectNode(node);
                startNodeDrag(node, e);
            });

            canvas.appendChild(node);

            // 将节点数据保存到AppState
            const nodeData = {
                id: node.dataset.nodeId,
                type: type,
                name: config.name,
                description: config.description,
                element: node,
                position: { x: x, y: y },
                config: {}, // 节点配置，可以通过属性面板修改
                pins: {
                    input: config.pins.input,
                    output: config.pins.output
                },
                status: 'idle'
            };
            AppState.nodes.set(node.dataset.nodeId, nodeData);

            updateDisplay();

            // 设置初始配置状态
            setTimeout(() => {
                const apiNodeDef = apiNodeDefinitions.get(type);
                if (apiNodeDef && apiNodeDef.frontend && apiNodeDef.frontend.properties && apiNodeDef.frontend.properties.panel && apiNodeDef.frontend.properties.panel.length > 0) {
                    // 有配置参数的节点，初始状态为未完善
                    updateNodeConfigStatus(node.dataset.nodeId, 'incomplete');
                } else {
                    // 没有配置参数的节点，状态为完善
                    updateNodeConfigStatus(node.dataset.nodeId, 'complete');
                }
            }, 100);

            console.log(`创建节点: ${config.name} #${nodeCounter}`);
            return node;
        }

        // 创建Pin
        function createPin(pinConfig, direction, nodeId, index) {
            const container = document.createElement('div');
            container.className = `pin-container ${direction}`;

            const pin = document.createElement('div');
            pin.className = `pin ${direction}`;
            pin.dataset.pinId = `${nodeId}_${direction}_${index}`;
            pin.dataset.pinName = pinConfig.name;
            pin.dataset.pinType = pinConfig.type;
            pin.dataset.nodeId = nodeId;
            pin.dataset.direction = direction;

            const label = document.createElement('div');
            label.className = 'pin-label';
            label.textContent = pinConfig.label;

            // Pin点击事件
            pin.addEventListener('click', (e) => {
                e.stopPropagation();
                handlePinClick(pin);
            });

            // Pin悬停事件
            pin.addEventListener('mouseenter', () => {
                if (isConnecting && sourcePin && canConnect(sourcePin, pin)) {
                    pin.style.background = '#4caf50';
                    pin.style.borderColor = '#4caf50';
                }
            });

            pin.addEventListener('mouseleave', () => {
                if (!pin.classList.contains('connected')) {
                    pin.style.background = 'white';
                    pin.style.borderColor = '#6c757d';
                }
            });

            if (direction === 'input') {
                container.appendChild(pin);
                container.appendChild(label);
            } else {
                container.appendChild(label);
                container.appendChild(pin);
            }

            return container;
        }

        // 处理Pin点击
        function handlePinClick(pin) {
            console.log(`Pin点击: ${pin.dataset.pinId}, 方向: ${pin.dataset.direction}, 连接模式: ${connectionMode}, 正在连接: ${isConnecting}`);

            if (!connectionMode && !isConnecting) {
                toggleConnectionMode();
            }

            if (!isConnecting) {
                // 开始连接
                if (pin.dataset.direction === 'output') {
                    console.log('开始连接，源Pin:', pin.dataset.pinId);
                    startConnection(pin);
                } else {
                    console.log('请先点击输出Pin开始连接');
                }
            } else {
                // 完成连接
                if (pin.dataset.direction === 'input' && sourcePin && canConnect(sourcePin, pin)) {
                    console.log(`创建连接: ${sourcePin.dataset.pinId} -> ${pin.dataset.pinId}`);
                    createConnection(sourcePin, pin);
                    cancelConnection(); // 连接成功后取消连接状态
                } else if (pin.dataset.direction === 'output') {
                    // 如果点击的是另一个输出Pin，重新开始连接
                    console.log('重新开始连接，新的源Pin:', pin.dataset.pinId);
                    cancelConnection();
                    startConnection(pin);
                } else {
                    console.log('无法连接:', {
                        direction: pin.dataset.direction,
                        hasSourcePin: !!sourcePin,
                        canConnect: sourcePin ? canConnect(sourcePin, pin) : false
                    });
                    // 不要取消连接，让用户可以继续尝试
                }
            }
        }

        // 开始连接
        function startConnection(outputPin) {
            sourcePin = outputPin;
            isConnecting = true;
            outputPin.style.background = '#ff9800';
            outputPin.style.borderColor = '#ff9800';

            // 创建临时连接线
            createTempConnection(outputPin);

            console.log('开始连接:', outputPin.dataset.pinId);
        }

        // 取消连接
        function cancelConnection() {
            if (sourcePin) {
                if (!sourcePin.classList.contains('connected')) {
                    sourcePin.style.background = 'white';
                    sourcePin.style.borderColor = '#6c757d';
                }
                sourcePin = null;
            }

            if (tempConnection) {
                removeTempConnection();
            }

            isConnecting = false;
            console.log('取消连接');
        }

        // 检查是否可以连接
        function canConnect(outputPin, inputPin) {
            // 不能连接到同一个节点
            if (outputPin.dataset.nodeId === inputPin.dataset.nodeId) {
                return false;
            }

            // 输出Pin只能连接到输入Pin
            if (outputPin.dataset.direction !== 'output' || inputPin.dataset.direction !== 'input') {
                return false;
            }

            // 检查是否已经存在连接
            const existingConnection = connections.find(conn =>
                conn.input.pinId === inputPin.dataset.pinId
            );

            if (existingConnection) {
                return false;
            }

            // 类型兼容性检查（简化版）
            const outputType = outputPin.dataset.pinType;
            const inputType = inputPin.dataset.pinType;

            if (outputType === 'any' || inputType === 'any' || outputType === inputType) {
                return true;
            }

            return false;
        }

        // 创建连接
        function createConnection(outputPin, inputPin) {
            connectionCounter++;

            const connection = {
                id: `connection_${connectionCounter}`,
                output: {
                    nodeId: outputPin.dataset.nodeId,
                    pinId: outputPin.dataset.pinId,
                    pinName: outputPin.dataset.pinName,
                    element: outputPin
                },
                input: {
                    nodeId: inputPin.dataset.nodeId,
                    pinId: inputPin.dataset.pinId,
                    pinName: inputPin.dataset.pinName,
                    element: inputPin
                },
                element: null
            };

            // 创建SVG连接线
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            path.setAttribute('class', 'connection-line');
            path.setAttribute('marker-end', 'url(#arrowhead)');
            path.dataset.connectionId = connection.id;

            // 连接线点击事件
            path.addEventListener('click', (e) => {
                e.stopPropagation();
                selectConnection(connection);
            });

            connection.element = path;
            connectionsGroup.appendChild(path);
            connections.push(connection);

            // 更新Pin状态
            outputPin.classList.add('connected');
            inputPin.classList.add('connected');
            outputPin.style.background = '#2196f3';
            outputPin.style.borderColor = '#2196f3';
            inputPin.style.background = '#2196f3';
            inputPin.style.borderColor = '#2196f3';

            // 更新连接线路径
            updateConnectionPath(connection);
            updateDisplay();

            console.log(`创建连接: ${outputPin.dataset.pinId} -> ${inputPin.dataset.pinId}`);
            return connection;
        }

        // 更新连接线路径（纯屏幕坐标系统，避免变换误差）
        function updateConnectionPath(connection) {
            const outputPin = connection.output.element;
            const inputPin = connection.input.element;

            if (!outputPin || !inputPin) {
                console.error(`连接线 ${connection.id} 的Pin元素不存在:`, {
                    outputPin: !!outputPin,
                    inputPin: !!inputPin,
                    outputPinId: connection.output.pinId,
                    inputPinId: connection.input.pinId
                });
                return;
            }

            // 检查元素是否还在DOM中
            if (!document.contains(outputPin) || !document.contains(inputPin)) {
                console.error(`连接线 ${connection.id} 的Pin元素已从DOM中移除:`, {
                    outputPinInDOM: document.contains(outputPin),
                    inputPinInDOM: document.contains(inputPin)
                });
                return;
            }

            // 使用纯屏幕坐标系统，完全避免变换累积误差
            const outputRect = outputPin.getBoundingClientRect();
            const inputRect = inputPin.getBoundingClientRect();
            const canvasContainer = document.querySelector('.canvas-container');
            const containerRect = canvasContainer.getBoundingClientRect();

            // 计算Pin在容器坐标系中的位置
            const startX = outputRect.left + outputRect.width / 2 - containerRect.left;
            const startY = outputRect.top + outputRect.height / 2 - containerRect.top;
            const endX = inputRect.left + inputRect.width / 2 - containerRect.left;
            const endY = inputRect.top + inputRect.height / 2 - containerRect.top;

            // 创建贝塞尔曲线路径
            const dx = endX - startX;
            const dy = endY - startY;
            const distance = Math.sqrt(dx * dx + dy * dy);
            const controlOffset = Math.min(100, Math.max(50, distance * 0.3));

            const path = `M ${startX} ${startY} C ${startX + controlOffset} ${startY}, ${endX - controlOffset} ${endY}, ${endX} ${endY}`;

            connection.element.setAttribute('d', path);

            // 确保连接线可见
            connection.element.style.pointerEvents = 'stroke';
            connection.element.style.strokeWidth = '2';
            connection.element.style.stroke = '#2196f3';
            connection.element.style.fill = 'none';
        }

        // 更新所有连接线
        function updateAllConnections() {
            connections.forEach(connection => {
                updateConnectionPath(connection);
            });
        }

        // 创建临时连接线
        function createTempConnection(outputPin) {
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            path.setAttribute('class', 'temp-connection');

            // 设置临时连接线样式，与正式连接线保持一致
            path.style.stroke = '#ff9800'; // 橙色，表示临时状态
            path.style.strokeWidth = '2';
            path.style.fill = 'none';
            path.style.strokeDasharray = '5,5'; // 虚线，表示临时状态
            path.style.pointerEvents = 'none';

            tempConnection = path;
            connectionsGroup.appendChild(path);
        }

        // 更新临时连接线（纯屏幕坐标系统）
        function updateTempConnection(mouseEvent) {
            if (!tempConnection || !sourcePin) return;

            // 使用纯屏幕坐标系统
            const sourcePinRect = sourcePin.getBoundingClientRect();
            const canvasContainer = document.querySelector('.canvas-container');
            const containerRect = canvasContainer.getBoundingClientRect();

            // 计算起点在容器坐标系中的位置
            const startX = sourcePinRect.left + sourcePinRect.width / 2 - containerRect.left;
            const startY = sourcePinRect.top + sourcePinRect.height / 2 - containerRect.top;

            // 计算鼠标在容器坐标系中的位置
            const endX = mouseEvent.clientX - containerRect.left;
            const endY = mouseEvent.clientY - containerRect.top;

            const dx = endX - startX;
            const controlOffset = Math.max(50, Math.abs(dx) * 0.5);

            const path = `M ${startX} ${startY} C ${startX + controlOffset} ${startY}, ${endX - controlOffset} ${endY}, ${endX} ${endY}`;

            tempConnection.setAttribute('d', path);
        }

        // 移除临时连接线
        function removeTempConnection() {
            if (tempConnection) {
                connectionsGroup.removeChild(tempConnection);
                tempConnection = null;
            }
        }

        // 删除连接
        function deleteConnection(connection) {
            if (confirm('确定要删除这个连接吗？')) {
                // 移除SVG元素
                if (connection.element && connection.element.parentNode) {
                    connection.element.parentNode.removeChild(connection.element);
                }

                // 更新Pin状态
                const outputPin = connection.output.element;
                const inputPin = connection.input.element;

                if (outputPin) {
                    outputPin.classList.remove('connected');
                    outputPin.style.background = 'white';
                    outputPin.style.borderColor = '#6c757d';
                }

                if (inputPin) {
                    inputPin.classList.remove('connected');
                    inputPin.style.background = 'white';
                    inputPin.style.borderColor = '#6c757d';
                }

                // 从数组中移除
                const index = connections.indexOf(connection);
                if (index > -1) {
                    connections.splice(index, 1);
                }

                clearSelection();
                updateDisplay();

                console.log('删除连接:', connection.id);
            }
        }

        // 节点拖拽
        function startNodeDrag(node, e) {
            isDraggingNode = true;

            // 获取节点当前在画布坐标系中的位置
            const currentLeft = parseFloat(node.style.left) || 0;
            const currentTop = parseFloat(node.style.top) || 0;

            // 计算鼠标相对于画布的位置（考虑缩放和平移）
            const canvasRect = canvas.getBoundingClientRect();
            const mouseCanvasX = (e.clientX - canvasRect.left - pan.x) / zoom;
            const mouseCanvasY = (e.clientY - canvasRect.top - pan.y) / zoom;

            // 计算鼠标相对于节点的偏移（在画布坐标系中）
            const offsetX = mouseCanvasX - currentLeft;
            const offsetY = mouseCanvasY - currentTop;

            node.classList.add('dragging');

            const mouseMoveHandler = (e) => {
                if (!isDraggingNode) return;

                // 计算鼠标在画布坐标系中的新位置
                const newMouseCanvasX = (e.clientX - canvasRect.left - pan.x) / zoom;
                const newMouseCanvasY = (e.clientY - canvasRect.top - pan.y) / zoom;

                // 计算节点的新位置（减去偏移量）
                const newX = newMouseCanvasX - offsetX;
                const newY = newMouseCanvasY - offsetY;

                // 无限画布：移除位置限制，允许节点移动到任何位置
                node.style.left = newX + 'px';
                node.style.top = newY + 'px';

                // 立即更新连接线，确保实时跟随
                updateNodeConnections(node.dataset.nodeId);
                updateDisplay();
            };

            const mouseUpHandler = () => {
                isDraggingNode = false;
                node.classList.remove('dragging');

                // 拖拽结束后强制更新连接线
                console.log('节点拖拽结束，强制更新连接线');
                setTimeout(() => {
                    updateNodeConnections(node.dataset.nodeId);
                }, 10); // 小延迟确保DOM完全更新

                document.removeEventListener('mousemove', mouseMoveHandler);
                document.removeEventListener('mouseup', mouseUpHandler);
            };

            document.addEventListener('mousemove', mouseMoveHandler);
            document.addEventListener('mouseup', mouseUpHandler);
        }

        // 更新节点相关的连接线
        function updateNodeConnections(nodeId) {
            const relatedConnections = connections.filter(connection =>
                connection.output.nodeId === nodeId || connection.input.nodeId === nodeId
            );

            if (relatedConnections.length > 0) {
                console.log(`🔄 拖拽中更新节点 ${nodeId} 的 ${relatedConnections.length} 个连接线`);
            }

            // 直接同步更新，确保实时响应
            relatedConnections.forEach(connection => {
                updateConnectionPath(connection);
            });
        }

        // 节点选择
        function selectNode(node) {
            clearSelection();
            selectedNode = node;
            node.classList.add('selected');
            showNodeProperties(node);
        }

        // 连接选择
        function selectConnection(connection) {
            clearSelection();
            selectedConnection = connection;
            connection.element.classList.add('selected');
            showConnectionProperties(connection);
        }

        function clearSelection() {
            if (selectedNode) {
                selectedNode.classList.remove('selected');
                selectedNode = null;
            }

            if (selectedConnection) {
                selectedConnection.element.classList.remove('selected');
                selectedConnection = null;
            }

            hideProperties();
        }

        // 属性面板
        function showNodeProperties(node) {
            const properties = document.getElementById('properties');
            const config = nodeTypes[node.dataset.nodeType];
            const apiNodeDef = apiNodeDefinitions.get(node.dataset.nodeType);

            // 获取节点当前的引脚配置（如果有自定义配置）
            const currentInputCount = node.dataset.inputPinCount ? parseInt(node.dataset.inputPinCount) : config.pins.input.length;
            const currentOutputCount = node.dataset.outputPinCount ? parseInt(node.dataset.outputPinCount) : config.pins.output.length;

            properties.innerHTML = `
                <h4>节点属性</h4>
                <div style="margin: 20px 0;">
                    <label>名称:</label>
                    <input type="text" value="${config.name} #${node.dataset.nodeId.split('_')[1]}"
                           style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #e1e5e9; border-radius: 4px;">
                </div>
                <div style="margin: 20px 0;">
                    <label>类型:</label>
                    <div style="padding: 8px; background: #f8f9fa; border-radius: 4px; margin-top: 5px;">${config.name}</div>
                </div>
                <div style="margin: 20px 0;">
                    <label>描述:</label>
                    <div style="padding: 8px; background: #f8f9fa; border-radius: 4px; margin-top: 5px;">${config.description}</div>
                </div>

                ${generatePinConfiguration(apiNodeDef, node, currentInputCount, currentOutputCount)}

                ${generateDynamicProperties(apiNodeDef, node)}

                <div style="margin: 20px 0;">
                    <button onclick="deleteNode(selectedNode)"
                            style="width: 100%; padding: 10px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        删除节点
                    </button>
                </div>
            `;

            // 添加事件监听器
            setTimeout(() => {
                const inputSlider = document.getElementById('inputPinCount');
                const outputSlider = document.getElementById('outputPinCount');
                const inputValue = document.getElementById('inputPinValue');
                const outputValue = document.getElementById('outputPinValue');
                const applyBtn = document.getElementById('applyPinChangesBtn');

                if (inputSlider && inputValue) {
                    inputSlider.addEventListener('input', function() {
                        inputValue.textContent = this.value;
                        console.log('输入引脚滑块变化:', this.value);
                    });
                }

                if (outputSlider && outputValue) {
                    outputSlider.addEventListener('input', function() {
                        outputValue.textContent = this.value;
                        console.log('输出引脚滑块变化:', this.value);
                    });
                }

                if (applyBtn) {
                    applyBtn.addEventListener('click', function() {
                        const nodeId = this.dataset.nodeId;
                        applyPinChanges(nodeId);
                    });
                }






                // 动态配置保存按钮
                const saveDynamicConfigBtn = document.getElementById('saveDynamicConfig');
                if (saveDynamicConfigBtn) {
                    saveDynamicConfigBtn.addEventListener('click', function() {
                        const nodeId = this.dataset.nodeId;
                        const apiNodeDef = apiNodeDefinitions.get(selectedNode.dataset.nodeType);

                        if (apiNodeDef && apiNodeDef.frontend && apiNodeDef.frontend.properties && apiNodeDef.frontend.properties.panel) {
                            const properties = apiNodeDef.frontend.properties.panel;
                            let savedCount = 0;

                            properties.forEach(prop => {
                                const propId = `prop_${prop.name}_${nodeId}`;
                                const element = document.getElementById(propId);

                                if (element) {
                                    let value;
                                    if (prop.type === 'checkbox') {
                                        value = element.checked;
                                    } else if (prop.type === 'number') {
                                        value = parseFloat(element.value) || prop.defaultValue || 0;
                                    } else {
                                        value = element.value;
                                    }

                                    updateNodeConfig(nodeId, prop.name, value);
                                    savedCount++;
                                }
                            });

                            // 更新配置状态灯
                            updateNodeConfigStatus(nodeId, 'complete');

                            alert(`已保存 ${savedCount} 个配置项！`);
                        }
                    });
                }
            }, 100); // 小延迟确保DOM元素已创建
        }

        // 计算节点尺寸
        function calculateNodeSize(maxPins) {
            const pinSpacing = 20; // 引脚间距
            const headerHeight = 45; // 节点头部高度（到引脚起始位置）
            const pinStartOffset = 5; // 引脚起始偏移
            const bottomPadding = 30; // 底部内边距（增加）
            const baseBodyHeight = 60; // 基础主体高度（增加节点主体区域）
            const minHeight = 120; // 最小高度
            const nodeWidth = 200; // 固定宽度

            // 计算引脚区域所需高度（增加一倍的增长比例）
            let pinsAreaHeight = baseBodyHeight; // 基础主体高度
            if (maxPins > 0) {
                // 引脚区域高度 = 基础高度 + 引脚起始偏移 + (引脚数量-1) * 间距 * 2 + 引脚高度 + 底部间距
                // 乘以2是为了让节点高度增长比引脚增长多一倍
                pinsAreaHeight = baseBodyHeight + pinStartOffset + (maxPins - 1) * pinSpacing * 2 + 16 + bottomPadding;
            }

            // 总高度 = 头部高度 + 引脚区域高度
            const calculatedHeight = headerHeight + pinsAreaHeight;
            const nodeHeight = Math.max(minHeight, calculatedHeight);

            console.log(`节点尺寸计算: 最大引脚数=${maxPins}, 头部=${headerHeight}, 基础主体=${baseBodyHeight}, 引脚区域=${pinsAreaHeight}, 总高度=${nodeHeight}`);

            return { width: nodeWidth, height: nodeHeight };
        }

        // 验证引脚位置是否在节点边界内
        function validatePinPositions(node, inputPinCount, outputPinCount, nodeHeight) {
            const pinStartY = 45; // 引脚起始Y位置（从图标下方开始）
            const pinSpacing = 20; // 引脚间距

            const maxPins = Math.max(inputPinCount, outputPinCount);
            if (maxPins > 0) {
                // 计算最后一个引脚的Y位置
                const lastPinY = pinStartY + (maxPins - 1) * pinSpacing + 16; // 16是引脚高度

                console.log(`引脚位置验证: 节点高度=${nodeHeight}, 引脚起始=${pinStartY}, 最后引脚Y=${lastPinY}, 是否超出=${lastPinY > nodeHeight ? '是' : '否'}`);

                if (lastPinY > nodeHeight) {
                    console.warn(`⚠️ 引脚超出节点边界! 最后引脚位置: ${lastPinY}, 节点高度: ${nodeHeight}`);
                } else {
                    console.log(`✅ 引脚位置正常，在节点边界内，剩余空间: ${nodeHeight - lastPinY}px`);
                }
            }
        }

        // 应用引脚更改
        function applyPinChanges(nodeId) {
            const inputCount = parseInt(document.getElementById('inputPinCount').value);
            const outputCount = parseInt(document.getElementById('outputPinCount').value);

            console.log(`应用引脚更改: 节点 ${nodeId}, 输入: ${inputCount}, 输出: ${outputCount}`);

            const node = document.querySelector(`[data-node-id="${nodeId}"]`);
            if (!node) {
                console.error('找不到节点:', nodeId);
                return;
            }

            // 更新节点数据
            node.dataset.inputPinCount = inputCount;
            node.dataset.outputPinCount = outputCount;

            // 删除无效连接
            removeInvalidConnections(nodeId, 'input', inputCount);
            removeInvalidConnections(nodeId, 'output', outputCount);

            // 重新生成节点
            regenerateNode(node);

            // 更新属性面板显示
            if (selectedNode === node) {
                showNodeProperties(node);
            }

            // 更新显示信息
            updateDisplay();

            // 显示成功提示
            showNotification('引脚配置已更新！', 'success');
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                background: ${type === 'success' ? '#4caf50' : '#2196f3'};
                color: white;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                z-index: 10000;
                font-size: 14px;
                transition: all 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 动态更新节点引脚数量（保留用于向后兼容）
        function updateNodePins(nodeId, direction, count) {
            console.log(`updateNodePins 被调用: ${nodeId}, ${direction}, ${count}`);
            // 这个函数现在主要由 applyPinChanges 替代
        }

        // 移除无效连接
        function removeInvalidConnections(nodeId, direction, newPinCount) {
            const connectionsToRemove = connections.filter(conn => {
                if (direction === 'input' && conn.input.nodeId === nodeId) {
                    const pinIndex = parseInt(conn.input.pinId.split('_').pop());
                    return pinIndex >= newPinCount;
                } else if (direction === 'output' && conn.output.nodeId === nodeId) {
                    const pinIndex = parseInt(conn.output.pinId.split('_').pop());
                    return pinIndex >= newPinCount;
                }
                return false;
            });

            connectionsToRemove.forEach(conn => {
                deleteConnection(conn);
            });
        }

        // 重新生成节点
        function regenerateNode(node) {
            const nodeType = node.dataset.nodeType;
            const config = nodeTypes[nodeType];
            const nodeId = node.dataset.nodeId;

            // 获取当前引脚数量
            const inputPinCount = parseInt(node.dataset.inputPinCount) || config.pins.input.length;
            const outputPinCount = parseInt(node.dataset.outputPinCount) || config.pins.output.length;

            // 计算新的节点尺寸
            const maxPins = Math.max(inputPinCount, outputPinCount);
            const { width, height } = calculateNodeSize(maxPins);

            // 更新节点尺寸
            node.style.width = width + 'px';
            node.style.height = height + 'px';

            // 重新创建引脚
            const inputPinsContainer = node.querySelector('.node-pins.input');
            const outputPinsContainer = node.querySelector('.node-pins.output');

            // 清空现有引脚
            inputPinsContainer.innerHTML = '';
            outputPinsContainer.innerHTML = '';

            // 创建新的输入引脚
            const apiNodeDef = apiNodeDefinitions.get(nodeType);

            for (let i = 0; i < inputPinCount; i++) {
                let pinConfig;

                // 对于动态引脚节点，需要特殊处理
                if (apiNodeDef && apiNodeDef.pins && apiNodeDef.pins.input && apiNodeDef.pins.input.dynamic) {
                    // 特殊处理文本连接节点
                    if (nodeType === 'text-concat-node') {
                        // 所有引脚都是文本输入引脚
                        pinConfig = {
                            name: `text${i + 1}`,
                            label: `Text ${i + 1}`,
                            dataType: 'string',
                            required: true,
                            description: `Text input ${i + 1} for concatenation`
                        };
                    } else {
                        // 其他动态引脚节点的通用处理
                        pinConfig = config.pins.input[i] || {
                            name: `input_${i}`,
                            label: `输入 ${i + 1}`,
                            type: 'any'
                        };
                    }
                } else {
                    // 固定引脚节点
                    pinConfig = config.pins.input[i] || {
                        name: `input_${i}`,
                        label: `输入 ${i + 1}`,
                        type: 'any'
                    };
                }

                const pinContainer = createPin(pinConfig, 'input', nodeId, i);
                inputPinsContainer.appendChild(pinContainer);
            }

            // 创建新的输出引脚
            for (let i = 0; i < outputPinCount; i++) {
                const pinConfig = config.pins.output[i] || {
                    name: `output_${i}`,
                    label: `输出 ${i + 1}`,
                    type: 'any'
                };
                const pinContainer = createPin(pinConfig, 'output', nodeId, i);
                outputPinsContainer.appendChild(pinContainer);
            }

            // 重新生成引脚后，需要更新连接线中的引脚元素引用
            updateConnectionReferences(nodeId);

            // 更新所有相关连接线
            updateNodeConnections(nodeId);

            console.log(`节点 ${nodeId} 重新生成完成，尺寸: ${width}x${height}, 输入引脚: ${inputPinCount}, 输出引脚: ${outputPinCount}`);

            // 验证引脚位置
            validatePinPositions(node, inputPinCount, outputPinCount, height);
        }

        // 更新连接线中的引脚元素引用
        function updateConnectionReferences(nodeId) {
            console.log(`更新节点 ${nodeId} 的连接线引脚引用`);

            connections.forEach(connection => {
                let updated = false;

                // 更新输出引脚引用
                if (connection.output.nodeId === nodeId) {
                    const newOutputPin = document.querySelector(`[data-pin-id="${connection.output.pinId}"]`);
                    if (newOutputPin) {
                        console.log(`更新输出引脚引用: ${connection.output.pinId}`);
                        connection.output.element = newOutputPin;
                        updated = true;
                    } else {
                        console.warn(`找不到输出引脚: ${connection.output.pinId}`);
                    }
                }

                // 更新输入引脚引用
                if (connection.input.nodeId === nodeId) {
                    const newInputPin = document.querySelector(`[data-pin-id="${connection.input.pinId}"]`);
                    if (newInputPin) {
                        console.log(`更新输入引脚引用: ${connection.input.pinId}`);
                        connection.input.element = newInputPin;
                        updated = true;
                    } else {
                        console.warn(`找不到输入引脚: ${connection.input.pinId}`);
                    }
                }

                if (updated) {
                    console.log(`连接线 ${connection.id} 的引脚引用已更新`);
                }
            });
        }

        // 状态灯控制函数
        function updateNodeStatus(nodeId, executionStatus, configStatus) {
            const node = document.querySelector(`[data-node-id="${nodeId}"]`);
            if (!node) return;

            const executionLight = node.querySelector('.execution-status');
            const configLight = node.querySelector('.config-status');

            // 只更新指定的状态
            if (executionStatus && executionLight) {
                executionLight.setAttribute('data-status', executionStatus);
                // 更新tooltip
                const executionTitles = {
                    'running': '执行状态: 运行中',
                    'waiting': '执行状态: 等待输入',
                    'error': '执行状态: 运行错误'
                };
                executionLight.setAttribute('title', executionTitles[executionStatus] || '执行状态: 未知');
                console.log(`节点 ${nodeId} 执行状态更新为: ${executionStatus}`);
            }

            if (configStatus && configLight) {
                configLight.setAttribute('data-status', configStatus);
                // 更新tooltip
                const configTitles = {
                    'complete': '配置状态: 配置完善',
                    'incomplete': '配置状态: 配置未完善',
                    'error': '配置状态: 配置错误'
                };
                configLight.setAttribute('title', configTitles[configStatus] || '配置状态: 未知');
                console.log(`节点 ${nodeId} 配置状态更新为: ${configStatus}`);
            }
        }

        // 更新节点配置状态灯
        function updateNodeConfigStatus(nodeId, configStatus) {
            const node = document.querySelector(`[data-node-id="${nodeId}"]`);
            if (!node) return;

            const configLight = node.querySelector('.config-status');
            if (configLight) {
                configLight.setAttribute('data-status', configStatus);

                // 更新tooltip
                const configTitles = {
                    'complete': '配置状态: 配置完善',
                    'incomplete': '配置状态: 配置未完善',
                    'error': '配置状态: 配置错误'
                };
                configLight.setAttribute('title', configTitles[configStatus]);

                console.log(`🔧 节点 ${nodeId} 配置状态灯更新为: ${configStatus}`);
            }
        }

        // 批量更新所有节点状态（示例函数）
        function updateAllNodeStatuses() {
            const nodes = document.querySelectorAll('.workflow-node');
            nodes.forEach(node => {
                const nodeId = node.dataset.nodeId;
                // 这里可以根据实际业务逻辑来判断状态
                // 示例：随机设置状态用于演示
                const executionStatuses = ['running', 'waiting', 'error'];
                const configStatuses = ['complete', 'incomplete', 'error'];

                const randomExecution = executionStatuses[Math.floor(Math.random() * executionStatuses.length)];
                const randomConfig = configStatuses[Math.floor(Math.random() * configStatuses.length)];

                updateNodeStatus(nodeId, randomExecution, randomConfig);
            });
        }

        // 根据节点配置检查配置状态
        function checkNodeConfigStatus(nodeId) {
            const node = document.querySelector(`[data-node-id="${nodeId}"]`);
            if (!node) return 'incomplete';

            // 这里可以添加实际的配置检查逻辑
            // 例如：检查必填字段、语法验证等

            // 示例逻辑：如果节点有连接则认为配置完善
            const hasConnections = connections.some(conn =>
                conn.input.nodeId === nodeId || conn.output.nodeId === nodeId
            );

            return hasConnections ? 'complete' : 'incomplete';
        }

        function showConnectionProperties(connection) {
            const properties = document.getElementById('properties');

            properties.innerHTML = `
                <h4>连接属性</h4>
                <div style="margin: 20px 0;">
                    <label>连接ID:</label>
                    <div style="padding: 8px; background: #f8f9fa; border-radius: 4px; margin-top: 5px;">${connection.id}</div>
                </div>
                <div style="margin: 20px 0;">
                    <label>输出:</label>
                    <div style="padding: 8px; background: #f8f9fa; border-radius: 4px; margin-top: 5px;">
                        ${connection.output.nodeId} → ${connection.output.pinName}
                    </div>
                </div>
                <div style="margin: 20px 0;">
                    <label>输入:</label>
                    <div style="padding: 8px; background: #f8f9fa; border-radius: 4px; margin-top: 5px;">
                        ${connection.input.nodeId} → ${connection.input.pinName}
                    </div>
                </div>
                <div style="margin: 20px 0;">
                    <button id="deleteConnectionBtn"
                            style="width: 100%; padding: 10px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        删除连接
                    </button>
                </div>
            `;

            // 添加删除按钮的事件监听器
            const deleteBtn = document.getElementById('deleteConnectionBtn');
            if (deleteBtn) {
                deleteBtn.addEventListener('click', function() {
                    deleteConnection(connection);
                });
            }
        }

        function hideProperties() {
            const properties = document.getElementById('properties');
            properties.innerHTML = `
                <div class="no-selection">
                    <i class="fas fa-mouse-pointer"></i>
                    <p>选择一个节点或连接来查看其属性</p>
                </div>
            `;
        }

        // 删除节点
        function deleteNode(node) {
            if (confirm('确定要删除这个节点吗？这将同时删除所有相关连接。')) {
                const nodeId = node.dataset.nodeId;

                // 删除相关连接
                const nodeConnections = connections.filter(conn =>
                    conn.output.nodeId === nodeId ||
                    conn.input.nodeId === nodeId
                );

                nodeConnections.forEach(connection => {
                    deleteConnection(connection);
                });

                // 从AppState中删除节点数据
                AppState.nodes.delete(nodeId);

                // 从工作流引擎中删除节点执行器
                if (window.workflowEngine && window.workflowEngine.nodeExecutors) {
                    window.workflowEngine.nodeExecutors.delete(nodeId);
                }

                // 删除DOM节点
                node.remove();
                clearSelection();
                updateDisplay();
                console.log('删除节点: ' + nodeId + ' (已从AppState和执行引擎中移除)');
            }
        }

        // 搜索过滤
        function filterNodes(query) {
            const items = document.querySelectorAll('.node-item');
            items.forEach(item => {
                const name = item.querySelector('.node-name').textContent.toLowerCase();
                const description = item.querySelector('.node-description').textContent.toLowerCase();
                const matches = name.includes(query.toLowerCase()) || description.includes(query.toLowerCase());
                item.style.display = matches ? 'flex' : 'none';
            });
        }

        // 变换更新
        function updateTransform() {
            canvas.style.transform = `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`;
            grid.style.transform = `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`;

            // 完全移除SVG变换，使用屏幕坐标计算
            // connectionsGroup.setAttribute('transform', '');

            // 平移或缩放后，需要更新所有连接线
            updateAllConnections();
        }

        // 显示更新
        function updateDisplay() {
            const nodeCount = canvas.querySelectorAll('.workflow-node').length;
            const connectionCount = connections.length;

            document.getElementById('node-count').textContent = nodeCount;
            document.getElementById('connection-count').textContent = connectionCount;
            document.getElementById('zoom-display').textContent = Math.round(zoom * 100) + '%';
            document.getElementById('pan-display').textContent = `${Math.round(pan.x)}, ${Math.round(pan.y)}`;
        }

        // 工具栏功能
        function zoomIn() {
            zoomToPoint(1.2, null); // 以画布中心缩放
        }

        function zoomOut() {
            zoomToPoint(0.8, null); // 以画布中心缩放
        }

        // 通用缩放函数，支持指定缩放中心点
        function zoomToPoint(factor, centerPoint) {
            const oldZoom = zoom;
            const newZoom = Math.max(0.1, Math.min(5, zoom * factor));

            if (Math.abs(newZoom - oldZoom) < 0.001) return;

            // 如果没有指定中心点，使用画布中心
            if (!centerPoint) {
                const rect = canvas.getBoundingClientRect();
                centerPoint = {
                    x: rect.width / 2,
                    y: rect.height / 2
                };
            }

            // 使用相同的简化缩放算法
            const zoomRatio = newZoom / oldZoom;

            // 调整平移量，使中心点位置保持不变
            const newPanX = centerPoint.x - (centerPoint.x - pan.x) * zoomRatio;
            const newPanY = centerPoint.y - (centerPoint.y - pan.y) * zoomRatio;

            // 应用新的缩放和平移
            zoom = newZoom;
            pan.x = newPanX;
            pan.y = newPanY;

            updateTransform();
            updateDisplay();
            updateAllConnections();
        }

        function resetView() {
            zoom = 1;
            pan = { x: 0, y: 0 };
            updateTransform();
            updateDisplay();
            updateAllConnections();
        }

        function clearCanvas() {
            if (confirm('确定要清空所有节点和连接吗？')) {
                // 清除所有连接
                connections.forEach(connection => {
                    if (connection.element && connection.element.parentNode) {
                        connection.element.parentNode.removeChild(connection.element);
                    }
                });
                connections = [];

                // 清除所有节点
                canvas.querySelectorAll('.workflow-node').forEach(node => node.remove());

                clearSelection();
                nodeCounter = 0;
                connectionCounter = 0;
                updateDisplay();
                console.log('画布已清空');
            }
        }

        function toggleGrid() {
            showGrid = !showGrid;
            grid.style.display = showGrid ? 'block' : 'none';
            const btn = document.getElementById('grid-btn');
            btn.classList.toggle('active', showGrid);
        }

        function toggleConnectionMode() {
            connectionMode = !connectionMode;
            const btn = document.getElementById('connection-btn');
            btn.classList.toggle('active', connectionMode);

            if (connectionMode) {
                console.log('连接模式已启用');
                showConnectionHint();
            } else {
                console.log('连接模式已禁用');
                if (isConnecting) {
                    cancelConnection();
                }
            }
        }

        // 键盘快捷键提示
        function showKeyboardShortcuts() {
            alert(`键盘快捷键和操作指南:

🎮 基本操作:
Delete - 删除选中的节点或连接
Escape - 取消当前操作/清除选择
Ctrl+C - 切换连接模式

🔍 缩放和导航:
鼠标滚轮 - 以鼠标为中心缩放 (AutoCAD式)
工具栏 +/- - 以画布中心缩放
拖拽空白区域 - 平移画布

🔗 连接操作:
点击输出Pin → 点击输入Pin - 创建连接
点击连接线 - 选中连接

🎨 画布特性:
• 无限大画布 - 节点可移动到任何位置
• 缩放范围: 10% - 500%
• 支持负坐标位置`);
        }

        // 导出工作流数据
        function exportWorkflow() {
            const workflow = {
                nodes: Array.from(canvas.querySelectorAll('.workflow-node')).map(node => ({
                    id: node.dataset.nodeId,
                    type: node.dataset.nodeType,
                    position: {
                        x: parseFloat(node.style.left),
                        y: parseFloat(node.style.top)
                    }
                })),
                connections: connections.map(conn => ({
                    id: conn.id,
                    output: {
                        nodeId: conn.output.nodeId,
                        pinName: conn.output.pinName
                    },
                    input: {
                        nodeId: conn.input.nodeId,
                        pinName: conn.input.pinName
                    }
                }))
            };

            const blob = new Blob([JSON.stringify(workflow, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `workflow-${new Date().toISOString().slice(0, 19)}.json`;
            a.click();
            URL.revokeObjectURL(url);

            console.log('工作流已导出');
        }

        // 添加工具栏按钮事件
        document.addEventListener('DOMContentLoaded', () => {
            // 添加导出按钮
            const toolbar = document.querySelector('.canvas-toolbar');
            const exportBtn = document.createElement('button');
            exportBtn.className = 'btn';
            exportBtn.innerHTML = '<i class="fas fa-download"></i> 导出';
            exportBtn.onclick = exportWorkflow;

            const helpBtn = document.createElement('button');
            helpBtn.className = 'btn';
            helpBtn.innerHTML = '<i class="fas fa-question"></i> 帮助';
            helpBtn.onclick = showKeyboardShortcuts;

            toolbar.insertBefore(exportBtn, toolbar.querySelector('.zoom-info'));
            toolbar.insertBefore(helpBtn, toolbar.querySelector('.zoom-info'));
        });

        // NodeExecutor class for executing individual nodes
        class NodeExecutor {
            constructor(nodeData, eventEmitter) {
                this.nodeData = nodeData;
                this.eventEmitter = eventEmitter;
                this.config = nodeData.config || {};
                this.status = 'idle';
                this.lastResult = null;
            }

            async execute(inputData = {}) {
                try {
                    this.setStatus('running');
                    console.log(`🔄 Executing node ${this.nodeData.id} with inputs:`, inputData);

                    // Simple execution for demo - just pass through data with some processing
                    const result = await this.executeSimpleLogic(inputData);

                    this.lastResult = result;
                    this.setStatus('completed');

                    console.log(`✅ Node ${this.nodeData.id} execution completed:`, result);
                    return result;

                } catch (error) {
                    this.setStatus('error');
                    console.error(`❌ Node ${this.nodeData.id} execution failed:`, error);
                    throw error;
                }
            }

            async executeSimpleLogic(inputData) {
                // Simple demo logic based on node type
                const outputs = {};

                switch (this.nodeData.type) {
                    case 'custom-task':
                        // Execute custom JavaScript code if available
                        if (this.config.customCode) {
                            try {
                                const func = new Function('inputs', 'config', this.config.customCode + '; return result || inputs;');
                                const result = func(inputData, this.config);
                                outputs.output1 = result;
                            } catch (error) {
                                outputs.output1 = `Error: ${error.message}`;
                            }
                        } else {
                            outputs.output1 = inputData.input1 || 'No input data';
                        }
                        break;

                    case 'conditional':
                        // Simple conditional logic
                        const condition = this.config.condition || 'true';
                        try {
                            const result = new Function('data', `return ${condition}`)(inputData.input1);
                            if (result) {
                                outputs.true = inputData.input1;
                            } else {
                                outputs.false = inputData.input1;
                            }
                        } catch (error) {
                            outputs.false = `Condition error: ${error.message}`;
                        }
                        break;

                    case 'multi-branch':
                        // Multi-branch logic
                        const branches = this.config.branches || ['branch1', 'branch2'];
                        const inputValue = inputData.input1 || '';
                        const selectedBranch = branches[Math.floor(Math.random() * branches.length)];
                        outputs[selectedBranch] = inputValue;
                        break;

                    case 'loop':
                        // Simple loop logic
                        const iterations = this.config.iterations || 1;
                        let loopResult = inputData.input1;
                        for (let i = 0; i < iterations; i++) {
                            loopResult = `${loopResult} (iteration ${i + 1})`;
                        }
                        outputs.output1 = loopResult;
                        break;

                    // 文本处理节点
                    case 'text-concat-node':
                        const text1 = String(inputData.text1 || '');
                        const text2 = String(inputData.text2 || '');
                        const separator = String(inputData.separator || '');
                        outputs.result = text1 + separator + text2;
                        break;

                    case 'text-split-node':
                        const textToSplit = String(inputData.text || '');
                        const splitSeparator = String(inputData.separator || ' ');
                        const parts = textToSplit.split(splitSeparator);
                        outputs.parts = parts;
                        outputs.count = parts.length;
                        break;

                    case 'text-replace-node':
                        const originalText = String(inputData.text || '');
                        const searchText = String(inputData.search || '');
                        const replaceText = String(inputData.replace || '');
                        outputs.result = originalText.replace(new RegExp(searchText, 'g'), replaceText);
                        break;

                    case 'text-case-node':
                        const textToConvert = String(inputData.text || '');
                        outputs.uppercase = textToConvert.toUpperCase();
                        outputs.lowercase = textToConvert.toLowerCase();
                        outputs.capitalize = textToConvert.charAt(0).toUpperCase() + textToConvert.slice(1).toLowerCase();
                        break;

                    case 'text-length-node':
                        const textToMeasure = String(inputData.text || '');
                        outputs.length = textToMeasure.length;
                        outputs.words = textToMeasure.trim() ? textToMeasure.trim().split(/\s+/).length : 0;
                        outputs.lines = textToMeasure.split('\n').length;
                        break;

                    // 数据输入节点
                    case 'manual-input-node':
                        outputs.text = this.config.inputText || '请在属性面板中设置文本';
                        outputs.number = this.config.inputNumber || 0;
                        break;

                    case 'constant-node':
                        outputs.value = this.config.constantValue !== undefined ? this.config.constantValue : '请设置常量值';
                        break;

                    case 'random-data-node':
                        outputs.number = Math.random() * (this.config.maxNumber || 100);
                        outputs.text = this.generateRandomText();
                        outputs.boolean = Math.random() > 0.5;
                        break;

                    case 'counter-node':
                        if (!this.config.counterValue) {
                            this.config.counterValue = 0;
                        }
                        this.config.counterValue++;
                        outputs.count = this.config.counterValue;
                        outputs.isFirst = this.config.counterValue === 1;
                        break;

                    // 数学计算节点
                    case 'math-basic-node':
                        const a = parseFloat(inputData.a) || 0;
                        const b = parseFloat(inputData.b) || 0;
                        outputs.add = a + b;
                        outputs.subtract = a - b;
                        outputs.multiply = a * b;
                        outputs.divide = b !== 0 ? a / b : 'Division by zero';
                        break;

                    case 'math-functions-node':
                        const value = parseFloat(inputData.value) || 0;
                        outputs.sin = Math.sin(value);
                        outputs.cos = Math.cos(value);
                        outputs.sqrt = value >= 0 ? Math.sqrt(value) : 'Invalid input';
                        outputs.abs = Math.abs(value);
                        outputs.log = value > 0 ? Math.log(value) : 'Invalid input';
                        break;

                    case 'math-stats-node':
                        let numbers = inputData.numbers;
                        if (!Array.isArray(numbers)) {
                            // 尝试从字符串解析数组
                            if (typeof numbers === 'string') {
                                numbers = numbers.split(',').map(n => parseFloat(n.trim())).filter(n => !isNaN(n));
                            } else {
                                numbers = [];
                            }
                        }

                        if (numbers.length > 0) {
                            outputs.sum = numbers.reduce((sum, num) => sum + num, 0);
                            outputs.average = outputs.sum / numbers.length;
                            outputs.min = Math.min(...numbers);
                            outputs.max = Math.max(...numbers);
                            outputs.count = numbers.length;
                        } else {
                            outputs.sum = 0;
                            outputs.average = 0;
                            outputs.min = 0;
                            outputs.max = 0;
                            outputs.count = 0;
                        }
                        break;

                    case 'math-compare-node':
                        const numA = parseFloat(inputData.a) || 0;
                        const numB = parseFloat(inputData.b) || 0;
                        outputs.equal = numA === numB;
                        outputs.greater = numA > numB;
                        outputs.less = numA < numB;
                        outputs.difference = Math.abs(numA - numB);
                        break;

                    default:
                        // Default: pass through
                        outputs.output1 = inputData.input1 || 'Default output';
                }

                return { outputs };
            }

            setStatus(status) {
                this.status = status;
                if (this.eventEmitter) {
                    this.eventEmitter.emit('node:status:changed', {
                        nodeId: this.nodeData.id,
                        status: status
                    });
                }
            }

            updateConfig(newConfig) {
                this.config = { ...this.config, ...newConfig };
                console.log(`Updated config for node ${this.nodeData.id}:`, this.config);
            }

            generateRandomText() {
                const words = ['apple', 'banana', 'cherry', 'dog', 'elephant', 'forest', 'guitar', 'house', 'island', 'jungle'];
                const wordCount = Math.floor(Math.random() * 5) + 1;
                const selectedWords = [];
                for (let i = 0; i < wordCount; i++) {
                    selectedWords.push(words[Math.floor(Math.random() * words.length)]);
                }
                return selectedWords.join(' ');
            }
        }

        // Simple WorkflowEngine for executing connected nodes
        class SimpleWorkflowEngine {
            constructor() {
                this.isExecuting = false;
                this.executionResults = new Map();
                this.nodeExecutors = new Map();
            }

            async executeWorkflow() {
                if (this.isExecuting) {
                    console.warn('Workflow is already executing');
                    return;
                }

                try {
                    this.isExecuting = true;
                    this.executionResults.clear();

                    console.log('🚀 Starting workflow execution...');

                    // Get all nodes from the current state
                    const allNodes = Array.from(AppState.nodes.values());

                    console.log(`📊 当前AppState中的节点数量: ${allNodes.length}`);
                    console.log('📋 节点列表:', allNodes.map(node => `${node.id} (${node.name})`));

                    if (allNodes.length === 0) {
                        console.warn('No nodes to execute');
                        return;
                    }

                    // Initialize node executors
                    this.initializeNodeExecutors(allNodes);

                    // Execute nodes in order (simple sequential execution for now)
                    await this.executeNodesSequentially(allNodes);

                    console.log('✅ Workflow execution completed');

                } catch (error) {
                    console.error('❌ Workflow execution failed:', error);
                } finally {
                    this.isExecuting = false;
                }
            }

            initializeNodeExecutors(nodes) {
                for (const nodeData of nodes) {
                    if (!this.nodeExecutors.has(nodeData.id)) {
                        const executor = new NodeExecutor(nodeData, null);
                        this.nodeExecutors.set(nodeData.id, executor);
                    }
                }
            }

            async executeNodesSequentially(nodes) {
                // 获取正确的执行顺序（基于连接的拓扑排序）
                const executionOrder = this.getExecutionOrder(nodes);

                console.log('📋 节点执行顺序:', executionOrder.map(nodeId => {
                    const nodeData = AppState.nodes.get(nodeId);
                    return `${nodeId} (${nodeData ? nodeData.name : 'Unknown'})`;
                }));

                for (const nodeId of executionOrder) {
                    await this.executeNode(nodeId);
                    // Add a small delay for visual effect
                    await new Promise(resolve => setTimeout(resolve, 800));
                }
            }

            // 基于连接关系计算节点执行顺序（拓扑排序）
            getExecutionOrder(nodes) {
                const nodeIds = nodes.map(node => node.id);
                const inDegree = new Map();
                const adjacencyList = new Map();

                // 初始化
                nodeIds.forEach(nodeId => {
                    inDegree.set(nodeId, 0);
                    adjacencyList.set(nodeId, []);
                });

                // 构建图和计算入度
                connections.forEach(conn => {
                    const fromNode = conn.output.nodeId;
                    const toNode = conn.input.nodeId;

                    if (nodeIds.includes(fromNode) && nodeIds.includes(toNode)) {
                        adjacencyList.get(fromNode).push(toNode);
                        inDegree.set(toNode, inDegree.get(toNode) + 1);
                    }
                });

                // 拓扑排序
                const queue = [];
                const result = [];

                // 找到所有入度为0的节点（没有依赖的节点）
                inDegree.forEach((degree, nodeId) => {
                    if (degree === 0) {
                        queue.push(nodeId);
                    }
                });

                // 如果没有入度为0的节点，说明可能有循环依赖或者没有连接
                if (queue.length === 0) {
                    console.warn('⚠️ 没有找到起始节点，使用原始顺序');
                    return nodeIds;
                }

                while (queue.length > 0) {
                    const currentNode = queue.shift();
                    result.push(currentNode);

                    // 处理当前节点的所有邻居
                    adjacencyList.get(currentNode).forEach(neighbor => {
                        inDegree.set(neighbor, inDegree.get(neighbor) - 1);
                        if (inDegree.get(neighbor) === 0) {
                            queue.push(neighbor);
                        }
                    });
                }

                // 如果结果长度不等于节点数量，说明有循环依赖
                if (result.length !== nodeIds.length) {
                    console.warn('⚠️ 检测到循环依赖或孤立节点，添加剩余节点');
                    nodeIds.forEach(nodeId => {
                        if (!result.includes(nodeId)) {
                            result.push(nodeId);
                        }
                    });
                }

                return result;
            }

            async executeNode(nodeId) {
                const executor = this.nodeExecutors.get(nodeId);
                if (!executor) {
                    console.error(`No executor found for node ${nodeId}`);
                    return;
                }

                try {
                    console.log(`🔄 Executing node: ${nodeId}`);

                    // Update visual status to executing
                    this.updateNodeVisualStatus(nodeId, 'executing');

                    // Get input data for this node (simplified)
                    const inputData = this.getNodeInputData(nodeId);

                    // Execute the node
                    const result = await executor.execute(inputData);

                    // Store result
                    this.executionResults.set(nodeId, result);

                    // Update visual status
                    this.updateNodeVisualStatus(nodeId, 'completed');

                    console.log(`✅ Node ${nodeId} executed successfully:`, result);

                } catch (error) {
                    console.error(`❌ Node ${nodeId} execution failed:`, error);
                    this.executionResults.set(nodeId, { error: error.message });
                    this.updateNodeVisualStatus(nodeId, 'error');
                }
            }

            getNodeInputData(nodeId) {
                // Simplified input data - in a real implementation, this would
                // get data from connected nodes based on the connections array
                const inputData = {};

                // Find connections that feed into this node
                const inputConnections = connections.filter(conn =>
                    conn.input && conn.input.nodeId === nodeId
                );

                for (const connection of inputConnections) {
                    const sourceNodeId = connection.output.nodeId;
                    const sourcePinName = connection.output.pinName;
                    const targetPinName = connection.input.pinName;

                    // Get data from source node's result
                    const sourceResult = this.executionResults.get(sourceNodeId);
                    if (sourceResult && sourceResult.outputs && sourceResult.outputs[sourcePinName]) {
                        inputData[targetPinName] = sourceResult.outputs[sourcePinName];
                    }
                }

                // If no input data, provide default
                if (Object.keys(inputData).length === 0) {
                    inputData.input1 = 'Default input data';
                }

                return inputData;
            }

            updateNodeVisualStatus(nodeId, status) {
                console.log(`🔍 尝试更新节点 ${nodeId} 状态为: ${status}`);

                const nodeData = AppState.nodes.get(nodeId);
                if (!nodeData) {
                    console.error(`❌ 未找到节点数据: ${nodeId}`);
                    return;
                }

                if (!nodeData.element) {
                    console.error(`❌ 节点元素不存在: ${nodeId}`);
                    return;
                }

                const element = nodeData.element;
                const executionLight = element.querySelector('.execution-status');

                if (!executionLight) {
                    console.error(`❌ 未找到执行状态灯: ${nodeId}`);
                    return;
                }

                // 映射执行状态到状态灯状态
                let lightStatus;
                switch (status) {
                    case 'executing':
                        lightStatus = 'running';
                        break;
                    case 'completed':
                        lightStatus = 'waiting'; // 执行完成后回到等待状态
                        break;
                    case 'error':
                        lightStatus = 'error';
                        break;
                    default:
                        lightStatus = 'waiting';
                }

                console.log(`🔄 节点 ${nodeId} 状态映射: ${status} → ${lightStatus}`);

                // 更新执行状态灯
                const oldStatus = executionLight.getAttribute('data-status');
                executionLight.setAttribute('data-status', lightStatus);

                // 更新tooltip
                const statusTitles = {
                    'running': '执行状态: 运行中',
                    'waiting': '执行状态: 等待输入',
                    'error': '执行状态: 运行错误'
                };
                executionLight.setAttribute('title', statusTitles[lightStatus]);

                console.log(`✅ 节点 ${nodeId} 状态灯已更新: ${oldStatus} → ${lightStatus}`);

                // 强制重绘
                executionLight.style.display = 'none';
                executionLight.offsetHeight; // 触发重排
                executionLight.style.display = '';
            }
        }

        // Create global workflow engine instance
        window.workflowEngine = new SimpleWorkflowEngine();

        // 节点配置更新函数
        function updateNodeConfig(nodeId, configKey, configValue) {
            const nodeData = AppState.nodes.get(nodeId);
            if (nodeData) {
                if (!nodeData.config) {
                    nodeData.config = {};
                }
                nodeData.config[configKey] = configValue;
                console.log(`节点 ${nodeId} 配置已更新:`, nodeData.config);
            }
        }

        // 全局函数，供属性面板调用
        window.updateNodeConfig = updateNodeConfig;

        // 检查节点是否有动态引脚配置
        function hasDynamicPins(apiNodeDef) {
            if (!apiNodeDef || !apiNodeDef.pins) {
                return false;
            }

            const inputDynamic = apiNodeDef.pins.input && apiNodeDef.pins.input.dynamic === true;
            const outputDynamic = apiNodeDef.pins.output && apiNodeDef.pins.output.dynamic === true;

            return inputDynamic || outputDynamic;
        }

        // 生成引脚配置HTML（仅对动态引脚节点）
        function generatePinConfiguration(apiNodeDef, node, currentInputCount, currentOutputCount) {
            // 检查是否需要显示引脚配置
            if (!hasDynamicPins(apiNodeDef)) {
                return `
                    <div style="margin: 20px 0;">
                        <label>引脚信息:</label>
                        <div style="padding: 8px; background: #e3f2fd; border: 1px solid #2196f3; border-radius: 4px; margin-top: 5px; font-size: 12px; color: #1976d2;">
                            <i class="fas fa-info-circle"></i> 此节点使用固定引脚配置，无需调整
                        </div>
                    </div>
                `;
            }

            // 对于动态引脚节点，显示完整的引脚配置
            return `
                <!-- 动态引脚数量控制 -->
                <div style="margin: 20px 0; border: 1px solid #e1e5e9; border-radius: 8px; padding: 15px; background: #f8f9fa;">
                    <h5 style="margin: 0 0 15px 0; color: #333;">引脚配置</h5>

                    ${apiNodeDef.pins.input && apiNodeDef.pins.input.dynamic ? `
                        <div style="margin: 15px 0;">
                            <label>输入引脚数量:</label>
                            <div style="display: flex; align-items: center; gap: 10px; margin-top: 5px;">
                                <input type="range" id="inputPinCount" min="${apiNodeDef.pins.input.min || 0}" max="${apiNodeDef.pins.input.max || 20}" value="${currentInputCount}"
                                       style="flex: 1;">
                                <span id="inputPinValue" style="min-width: 30px; font-weight: bold;">${currentInputCount}</span>
                            </div>
                        </div>
                    ` : ''}

                    ${apiNodeDef.pins.output && apiNodeDef.pins.output.dynamic ? `
                        <div style="margin: 15px 0;">
                            <label>输出引脚数量:</label>
                            <div style="display: flex; align-items: center; gap: 10px; margin-top: 5px;">
                                <input type="range" id="outputPinCount" min="${apiNodeDef.pins.output.min || 0}" max="${apiNodeDef.pins.output.max || 20}" value="${currentOutputCount}"
                                       style="flex: 1;">
                                <span id="outputPinValue" style="min-width: 30px; font-weight: bold;">${currentOutputCount}</span>
                            </div>
                        </div>
                    ` : ''}

                    <div style="margin: 15px 0;">
                        <button id="applyPinChangesBtn" data-node-id="${node.dataset.nodeId}"
                                style="width: 100%; padding: 10px; background: #2196f3; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">
                            应用引脚更改
                        </button>
                    </div>

                    <div style="margin: 15px 0; font-size: 12px; color: #666;">
                        调整滑块后点击"应用引脚更改"按钮生效
                    </div>
                </div>

                <div style="margin: 20px 0;">
                    <label>当前引脚状态:</label>
                    <div style="padding: 8px; background: white; border: 1px solid #e1e5e9; border-radius: 4px; margin-top: 5px; font-size: 12px;">
                        输入: ${currentInputCount} 个 | 输出: ${currentOutputCount} 个
                    </div>
                </div>
            `;
        }

        // 生成动态属性配置HTML
        function generateDynamicProperties(apiNodeDef, node) {
            if (!apiNodeDef || !apiNodeDef.frontend || !apiNodeDef.frontend.properties || !apiNodeDef.frontend.properties.panel) {
                return '';
            }

            const properties = apiNodeDef.frontend.properties.panel;
            if (properties.length === 0) {
                return '';
            }

            // 获取节点的当前配置
            const nodeData = AppState.nodes.get(node.dataset.nodeId);
            const currentConfig = nodeData ? nodeData.config || {} : {};

            console.log(`🔧 生成属性面板 - 节点 ${node.dataset.nodeId}:`, {
                nodeData: nodeData,
                currentConfig: currentConfig,
                propertiesCount: properties.length
            });

            let html = `
                <div style="margin: 20px 0; border: 1px solid #e1e5e9; border-radius: 8px; padding: 15px; background: #f8f9fa;">
                    <h5 style="margin: 0 0 15px 0; color: #333;">节点配置</h5>
            `;

            properties.forEach((prop, index) => {
                const propId = `prop_${prop.name}_${node.dataset.nodeId}`;

                // 获取当前值：优先使用保存的配置，其次使用默认值
                const currentValue = currentConfig.hasOwnProperty(prop.name) ? currentConfig[prop.name] : prop.defaultValue;

                html += `<div style="margin: 15px 0;">`;
                html += `<label for="${propId}">${prop.label}:</label>`;

                if (prop.description) {
                    html += `<div style="font-size: 12px; color: #666; margin-bottom: 5px;">${prop.description}</div>`;
                }

                switch (prop.type) {
                    case 'text':
                        html += `<input type="text" id="${propId}" value="${currentValue || ''}"
                                       style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #e1e5e9; border-radius: 4px;">`;
                        break;

                    case 'textarea':
                        html += `<textarea id="${propId}" rows="4"
                                          style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #e1e5e9; border-radius: 4px; font-family: monospace; font-size: 12px;">${currentValue || ''}</textarea>`;
                        break;

                    case 'number':
                        const min = prop.validation?.min || '';
                        const max = prop.validation?.max || '';
                        const numberValue = currentValue !== undefined ? currentValue : (prop.defaultValue || 0);
                        html += `<input type="number" id="${propId}" value="${numberValue}"
                                       min="${min}" max="${max}"
                                       style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #e1e5e9; border-radius: 4px;">`;
                        break;

                    case 'checkbox':
                        const isChecked = currentValue !== undefined ? currentValue : prop.defaultValue;
                        const checked = isChecked ? 'checked' : '';
                        html += `<label style="display: flex; align-items: center; margin-top: 5px;">
                                   <input type="checkbox" id="${propId}" ${checked} style="margin-right: 8px;">
                                   ${prop.label}
                                 </label>`;
                        break;

                    case 'select':
                        html += `<select id="${propId}" style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #e1e5e9; border-radius: 4px;">`;
                        if (prop.options) {
                            prop.options.forEach(option => {
                                const isSelected = currentValue !== undefined ? (option.value === currentValue) : (option.value === prop.defaultValue);
                                const selected = isSelected ? 'selected' : '';
                                html += `<option value="${option.value}" ${selected}>${option.label}</option>`;
                            });
                        }
                        html += `</select>`;
                        break;

                    default:
                        html += `<input type="text" id="${propId}" value="${currentValue || ''}"
                                       style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #e1e5e9; border-radius: 4px;">`;
                }

                html += `</div>`;
            });

            html += `
                    <div style="margin: 15px 0;">
                        <button id="saveDynamicConfig" data-node-id="${node.dataset.nodeId}"
                                style="width: 100%; padding: 10px; background: #4caf50; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">
                            保存配置
                        </button>
                    </div>
                </div>
            `;

            return html;
        }

        // Execute workflow demo function
        async function executeWorkflowDemo() {
            console.log('🔍 执行前检查 - AppState节点数量:', AppState.nodes.size);
            console.log('🔍 执行前检查 - DOM节点数量:', document.querySelectorAll('[data-node-id]').length);

            if (AppState.nodes.size === 0) {
                alert('请先添加一些节点到画布上！');
                return;
            }

            console.log('🚀 开始执行工作流演示...');

            try {
                // 使用新的工作流引擎执行
                await window.workflowEngine.executeWorkflow();

                // 显示执行结果
                setTimeout(() => {
                    const results = window.workflowEngine.executionResults;
                    let resultText = '工作流执行完成！\n\n执行结果:\n';

                    results.forEach((result, nodeId) => {
                        resultText += `\n节点 ${nodeId}:\n`;
                        if (result.error) {
                            resultText += `  错误: ${result.error}\n`;
                        } else if (result.outputs) {
                            Object.entries(result.outputs).forEach(([key, value]) => {
                                resultText += `  ${key}: ${JSON.stringify(value)}\n`;
                            });
                        }
                    });

                    alert(resultText);
                }, 1000);

            } catch (error) {
                console.error('工作流执行失败:', error);
                alert(`工作流执行失败: ${error.message}`);
            }
        }

        console.log('Dynamic Workflow System v1.0 - 连接系统脚本已加载');
    </script>
</body>
</html>
