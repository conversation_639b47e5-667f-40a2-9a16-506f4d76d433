{"id": "manual-input-node", "name": "Manual Input", "type": "input", "version": "1.0.0", "description": "Allows manual input of text and numeric data", "metadata": {"category": "Data Input", "tags": ["input", "manual", "data", "source"], "author": "Dynamic Workflow System", "documentation": "Provides user-configurable input values for workflow testing and data entry"}, "pins": {"input": {"count": 0, "dynamic": false, "definitions": []}, "output": {"count": 3, "dynamic": false, "definitions": [{"name": "text", "label": "Text Output", "dataType": "string", "description": "User-entered text value"}, {"name": "number", "label": "Number Output", "dataType": "number", "description": "User-entered numeric value"}, {"name": "combined", "label": "Combined Output", "dataType": "any", "description": "Combined text and number as object"}]}}, "visual": {"icon": {"type": "unicode", "value": "✏️", "color": "#607D8B"}, "shape": {"type": "rectangle"}, "sizing": {"baseWidth": 120, "baseHeight": 90, "pinSpacing": 18}, "colors": {"background": "#ECEFF1", "border": "#607D8B", "text": "#263238"}}, "backend": {"endpoint": "/api/nodes/manual-input", "method": "POST", "code": "async function execute(req, res) {\n  try {\n    const { config } = req.body;\n    \n    // Get configured values\n    const textValue = String(config.inputText || '');\n    const numberValue = parseFloat(config.inputNumber) || 0;\n    \n    // Create combined output\n    const combined = {\n      text: textValue,\n      number: numberValue,\n      timestamp: new Date().toISOString()\n    };\n    \n    res.json({\n      success: true,\n      outputs: {\n        text: textValue,\n        number: numberValue,\n        combined: combined\n      },\n      metadata: {\n        inputSource: 'manual',\n        configuredAt: Date.now()\n      }\n    });\n  } catch (error) {\n    console.error('Manual input error:', error);\n    res.status(500).json({\n      success: false,\n      error: error.message\n    });\n  }\n}", "dependencies": []}, "frontend": {"executable": {"code": "class ManualInputNode {\n  constructor(nodeData, eventEmitter) {\n    this.nodeData = nodeData;\n    this.eventEmitter = eventEmitter;\n    this.config = nodeData.config || {};\n  }\n  \n  async execute(inputs) {\n    try {\n      // Get configured values\n      const textValue = String(this.config.inputText || '');\n      const numberValue = parseFloat(this.config.inputNumber) || 0;\n      \n      // Create combined output\n      const combined = {\n        text: textValue,\n        number: numberValue,\n        timestamp: new Date().toISOString()\n      };\n      \n      return {\n        outputs: {\n          text: textValue,\n          number: numberValue,\n          combined: combined\n        }\n      };\n    } catch (error) {\n      throw new Error(`Manual input failed: ${error.message}`);\n    }\n  }\n  \n  updateConfig(newConfig) {\n    this.config = { ...this.config, ...newConfig };\n  }\n}", "dependencies": []}, "properties": {"panel": [{"type": "textarea", "name": "inputText", "label": "Text Input", "defaultValue": "Hello World", "validation": {"required": false, "maxLength": 1000}, "description": "Enter text data to output"}, {"type": "number", "name": "inputNumber", "label": "Number Input", "defaultValue": 42, "validation": {"required": false, "min": -999999, "max": 999999}, "description": "Enter numeric data to output"}, {"type": "select", "name": "outputFormat", "label": "Output Format", "defaultValue": "separate", "options": [{"value": "separate", "label": "Separate Outputs"}, {"value": "combined", "label": "Combined Object"}, {"value": "json", "label": "JSON String"}], "description": "How to format the output data"}]}}}