{"id": "conditional-node", "name": "条件节点", "type": "conditional", "version": "1.0.0", "description": "基于条件进行分支决策", "metadata": {"category": "控制流", "tags": ["condition", "decision", "branching", "logic"], "author": "Dynamic Workflow System", "documentation": "Evaluates JavaScript expressions and routes data based on boolean result"}, "pins": {"input": {"count": 2, "dynamic": false, "definitions": [{"name": "data", "label": "Input Data", "dataType": "any", "required": true, "description": "Data to evaluate and pass through"}, {"name": "condition", "label": "Condition", "dataType": "string", "required": true, "description": "JavaScript expression to evaluate (e.g., 'data.value > 10')"}]}, "output": {"count": 2, "dynamic": false, "definitions": [{"name": "true", "label": "True Branch", "dataType": "any", "description": "Output when condition evaluates to true"}, {"name": "false", "label": "False Branch", "dataType": "any", "description": "Output when condition evaluates to false"}]}}, "visual": {"icon": {"type": "unicode", "value": "🔀", "color": "#4CAF50"}, "shape": {"type": "diamond"}, "sizing": {"baseWidth": 120, "baseHeight": 80, "pinSpacing": 25, "dynamicResize": false}, "colors": {"background": "#E8F5E8", "border": "#4CAF50", "text": "#2E7D32"}}, "backend": {"endpoint": "/api/nodes/conditional", "method": "POST", "code": "async function execute(req, res) {\n  try {\n    const { data, condition } = req.body.inputs;\n    \n    // Create a safe evaluation context\n    const context = { data };\n    \n    // Evaluate the condition safely\n    const result = new Function('data', `return ${condition}`)(data);\n    \n    // Route to appropriate output\n    const outputs = {};\n    if (result) {\n      outputs.true = data;\n    } else {\n      outputs.false = data;\n    }\n    \n    res.json({ success: true, outputs });\n  } catch (error) {\n    res.status(400).json({ success: false, error: error.message });\n  }\n}", "dependencies": [], "middleware": ["express.json()"]}, "frontend": {"executable": {"code": "class ConditionalNode {\n  constructor(nodeId, config) {\n    this.nodeId = nodeId;\n    this.config = config;\n    this.inputs = {};\n    this.outputs = {};\n  }\n  \n  async execute() {\n    if (!this.inputs.data || !this.inputs.condition) {\n      return; // Wait for all required inputs\n    }\n    \n    try {\n      const condition = this.inputs.condition;\n      const data = this.inputs.data;\n      \n      // Evaluate condition\n      const result = new Function('data', `return ${condition}`)(data);\n      \n      // Send to appropriate output\n      if (result) {\n        this.sendOutput('true', data);\n      } else {\n        this.sendOutput('false', data);\n      }\n      \n      // Clear inputs for next execution\n      this.inputs = {};\n    } catch (error) {\n      console.error('Conditional node error:', error);\n    }\n  }\n  \n  receiveInput(pinName, data) {\n    this.inputs[pinName] = data;\n    this.execute();\n  }\n  \n  sendOutput(pinName, data) {\n    if (this.outputs[pinName]) {\n      this.outputs[pinName].forEach(connection => {\n        connection.targetNode.receiveInput(connection.targetPin, data);\n      });\n    }\n  }\n}", "dependencies": []}, "properties": {"panel": [{"type": "textarea", "name": "defaultCondition", "label": "De<PERSON>ult Condition", "defaultValue": "data.value > 0", "validation": {"required": false}}, {"type": "text", "name": "description", "label": "Node Description", "defaultValue": "Conditional logic node"}]}}}