﻿/**
 * Workflow Execution Engine for Dynamic Workflow System
 * Manages the execution of workflow nodes and data flow
 */

class WorkflowEngine {
  constructor(eventEmitter, nodeManager, connectionManager) {
    this.eventEmitter = eventEmitter;
    this.nodeManager = nodeManager;
    this.connectionManager = connectionManager;

    // Execution state
    this.isExecuting = false;
    this.executionQueue = [];
    this.executionResults = new Map();
    this.nodeExecutors = new Map();

    // Data flow management
    this.dataBuffer = new Map(); // Stores data waiting to be processed
    this.executionOrder = [];

    this.setupEventListeners();
  }

  setupEventListeners() {
    // Listen for workflow execution requests
    this.eventEmitter.on('workflow:execute', () => {
      this.executeWorkflow();
    });

    // Listen for node configuration updates
    this.eventEmitter.on('node:config:update', (data) => {
      this.updateNodeConfig(data.nodeId, data.config);
    });

    // Listen for connection changes
    this.eventEmitter.on('connection:created', () => {
      this.updateExecutionOrder();
    });

    this.eventEmitter.on('connection:deleted', () => {
      this.updateExecutionOrder();
    });
  }

  /**
   * Execute the entire workflow
   */
  async executeWorkflow() {
    if (this.isExecuting) {
      console.warn('Workflow is already executing');
      return;
    }

    try {
      this.isExecuting = true;
      this.executionResults.clear();

      console.log('🚀 Starting workflow execution...');
      this.eventEmitter.emit('workflow:execution:started');

      // Update execution order based on current connections
      this.updateExecutionOrder();

      // Initialize all node executors
      this.initializeNodeExecutors();

      // Execute nodes in topological order
      await this.executeNodesInOrder();

      console.log('✅ Workflow execution completed');
      this.eventEmitter.emit('workflow:execution:completed', this.executionResults);

    } catch (error) {
      console.error('❌ Workflow execution failed:', error);
      this.eventEmitter.emit('workflow:execution:failed', error);
    } finally {
      this.isExecuting = false;
    }
  }

  /**
   * Initialize node executors for all nodes
   */
  initializeNodeExecutors() {
    const allNodes = this.nodeManager.getAllNodes();

    for (const node of allNodes) {
      if (!this.nodeExecutors.has(node.id)) {
        const executor = new NodeExecutor(node, this.eventEmitter);
        this.nodeExecutors.set(node.id, executor);
      }
    }
  }

  /**
   * Execute nodes in topological order
   */
  async executeNodesInOrder() {
    const executionOrder = this.connectionManager.getExecutionOrder();
    const allNodes = this.nodeManager.getAllNodes();

    // Handle nodes without connections (isolated nodes)
    const connectedNodes = new Set(executionOrder);
    const isolatedNodes = allNodes.filter(node => !connectedNodes.has(node.id));

    // Execute isolated nodes first
    for (const node of isolatedNodes) {
      await this.executeNode(node.id);
    }

    // Execute connected nodes in order
    for (const nodeId of executionOrder) {
      await this.executeNode(nodeId);
    }
  }

  /**
   * Execute a single node
   */
  async executeNode(nodeId) {
    const executor = this.nodeExecutors.get(nodeId);
    if (!executor) {
      console.error(`No executor found for node ${nodeId}`);
      return;
    }

    try {
      console.log(`🔄 Executing node: ${nodeId}`);
      this.eventEmitter.emit('node:execution:started', nodeId);

      // Get input data for this node
      const inputData = this.getNodeInputData(nodeId);

      // Execute the node
      const result = await executor.execute(inputData);

      // Store result
      this.executionResults.set(nodeId, result);

      // Transmit output data to connected nodes
      this.transmitNodeOutput(nodeId, result);

      console.log(`✅ Node ${nodeId} executed successfully`);
      this.eventEmitter.emit('node:execution:completed', { nodeId, result });

    } catch (error) {
      console.error(`❌ Node ${nodeId} execution failed:`, error);
      this.executionResults.set(nodeId, { error: error.message });
      this.eventEmitter.emit('node:execution:failed', { nodeId, error });
    }
  }

  /**
   * Get input data for a node from connected outputs
   */
  getNodeInputData(nodeId) {
    const inputData = {};
    const connections = this.connectionManager.getAllConnections();

    // Find all connections that feed into this node
    const inputConnections = connections.filter(conn =>
      conn.target && conn.target.nodeId === nodeId
    );

    for (const connection of inputConnections) {
      const sourceNodeId = connection.source.nodeId;
      const sourcePinName = connection.source.pinName;
      const targetPinName = connection.target.pinName;

      // Get data from source node's result
      const sourceResult = this.executionResults.get(sourceNodeId);
      if (sourceResult && sourceResult.outputs && sourceResult.outputs[sourcePinName]) {
        inputData[targetPinName] = sourceResult.outputs[sourcePinName];
      }
    }

    return inputData;
  }

  /**
   * Transmit node output to connected nodes
   */
  transmitNodeOutput(nodeId, result) {
    if (!result || !result.outputs) return;

    const connections = this.connectionManager.getAllConnections();

    // Find all connections that originate from this node
    const outputConnections = connections.filter(conn =>
      conn.source && conn.source.nodeId === nodeId
    );

    for (const connection of outputConnections) {
      const sourcePinName = connection.source.pinName;
      const outputData = result.outputs[sourcePinName];

      if (outputData !== undefined) {
        // Animate the connection to show data flow
        this.connectionManager.animateConnection(connection.id);

        // Emit data transmission event
        this.eventEmitter.emit('data:transmitted', {
          connectionId: connection.id,
          sourceNodeId: nodeId,
          sourcePinName: sourcePinName,
          targetNodeId: connection.target.nodeId,
          targetPinName: connection.target.pinName,
          data: outputData
        });
      }
    }
  }

  /**
   * Update execution order when connections change
   */
  updateExecutionOrder() {
    this.executionOrder = this.connectionManager.calculateExecutionOrder();
  }

  /**
   * Update node configuration
   */
  updateNodeConfig(nodeId, config) {
    const executor = this.nodeExecutors.get(nodeId);
    if (executor) {
      executor.updateConfig(config);
    }
  }

  /**
   * Stop workflow execution
   */
  stopExecution() {
    this.isExecuting = false;
    this.eventEmitter.emit('workflow:execution:stopped');
  }

  /**
   * Get execution results
   */
  getExecutionResults() {
    return new Map(this.executionResults);
  }

  /**
   * Clear execution state
   */
  clearExecutionState() {
    this.executionResults.clear();
    this.dataBuffer.clear();
    this.nodeExecutors.clear();
  }
}
