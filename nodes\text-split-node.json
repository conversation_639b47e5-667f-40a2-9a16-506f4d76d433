{"id": "text-split-node", "name": "Text Split", "type": "transform", "version": "1.0.0", "description": "Splits text string into array using specified separator", "metadata": {"category": "Text Processing", "tags": ["text", "string", "split", "array"], "author": "Dynamic Workflow System", "documentation": "Divides input text into an array of substrings using a separator"}, "pins": {"input": {"count": 2, "dynamic": false, "definitions": [{"name": "text", "label": "Input Text", "dataType": "string", "required": true, "description": "Text string to split"}, {"name": "separator", "label": "Separator", "dataType": "string", "required": false, "description": "Character or string to split on"}]}, "output": {"count": 2, "dynamic": false, "definitions": [{"name": "parts", "label": "Text Parts", "dataType": "array", "description": "Array of split text parts"}, {"name": "count", "label": "Part Count", "dataType": "number", "description": "Number of parts created"}]}}, "visual": {"icon": {"type": "unicode", "value": "✂️", "color": "#3F51B5"}, "shape": {"type": "rectangle"}, "sizing": {"baseWidth": 130, "baseHeight": 80, "pinSpacing": 20}, "colors": {"background": "#E8EAF6", "border": "#3F51B5", "text": "#1A237E"}}, "backend": {"endpoint": "/api/nodes/text-split", "method": "POST", "code": "async function execute(req, res) {\n  try {\n    const { inputs, config } = req.body;\n    \n    // Get input values\n    const text = String(inputs.text || '');\n    const separator = String(inputs.separator !== undefined ? inputs.separator : (config.defaultSeparator || ' '));\n    \n    // Split text\n    const parts = text.split(separator);\n    \n    // Apply filters if configured\n    let filteredParts = parts;\n    if (config.removeEmpty) {\n      filteredParts = parts.filter(part => part.trim() !== '');\n    }\n    if (config.trimParts) {\n      filteredParts = filteredParts.map(part => part.trim());\n    }\n    \n    res.json({\n      success: true,\n      outputs: {\n        parts: filteredParts,\n        count: filteredParts.length\n      },\n      metadata: {\n        originalCount: parts.length,\n        filteredCount: filteredParts.length\n      }\n    });\n  } catch (error) {\n    console.error('Text split error:', error);\n    res.status(500).json({\n      success: false,\n      error: error.message\n    });\n  }\n}", "dependencies": []}, "frontend": {"executable": {"code": "class TextSplitNode {\n  constructor(nodeData, eventEmitter) {\n    this.nodeData = nodeData;\n    this.eventEmitter = eventEmitter;\n    this.config = nodeData.config || {};\n  }\n  \n  async execute(inputs) {\n    try {\n      // Get input values\n      const text = String(inputs.text || '');\n      const separator = String(inputs.separator !== undefined ? inputs.separator : (this.config.defaultSeparator || ' '));\n      \n      // Split text\n      const parts = text.split(separator);\n      \n      // Apply filters if configured\n      let filteredParts = parts;\n      if (this.config.removeEmpty) {\n        filteredParts = parts.filter(part => part.trim() !== '');\n      }\n      if (this.config.trimParts) {\n        filteredParts = filteredParts.map(part => part.trim());\n      }\n      \n      return {\n        outputs: {\n          parts: filteredParts,\n          count: filteredParts.length\n        }\n      };\n    } catch (error) {\n      throw new Error(`Text split failed: ${error.message}`);\n    }\n  }\n  \n  updateConfig(newConfig) {\n    this.config = { ...this.config, ...newConfig };\n  }\n}", "dependencies": []}, "properties": {"panel": [{"type": "text", "name": "defaultSeparator", "label": "<PERSON><PERSON><PERSON>", "defaultValue": " ", "validation": {"required": false}, "description": "Default separator when separator input is not connected"}, {"type": "checkbox", "name": "removeEmpty", "label": "Remove Empty Parts", "defaultValue": false, "description": "Remove empty strings from the result array"}, {"type": "checkbox", "name": "trimParts", "label": "Trim Parts", "defaultValue": false, "description": "Remove leading and trailing whitespace from each part"}, {"type": "number", "name": "maxParts", "label": "Maximum Parts", "defaultValue": 0, "validation": {"min": 0, "max": 1000}, "description": "Limit number of parts (0 = no limit)"}]}}}